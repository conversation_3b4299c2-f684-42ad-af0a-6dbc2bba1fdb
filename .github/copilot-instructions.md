# 核心技术栈

## 前端框架

- Vue 3.5.13: 使用最新版本的Vue框架
- 组合式API: 项目采用<script setup>语法和组合式API开发模式
- TypeScript: 全面使用TypeScript进行类型检查

## 构建工具

- Vite: 作为主要构建工具，提供快速的开发体验
- pnpm: 作为包管理工具

## UI框架与样式

- Ant Design Vue: 使用ant-design-vue作为UI组件库
- TailwindCSS 4.x: 使用最新版本的TailwindCSS进行样式开发
- LightningCSS: 用于CSS转换，支持原生CSS规则嵌套

## 状态管理

- Pinia 3.x: 用于状态管理
- pinia-plugin-persistedstate: 用于Pinia状态持久化

## 路由

- Vue Router 4.5.0: 用于SPA路由管理
- unplugin-vue-router: 自动生成路由配置

## 数据获取

- TanStack Vue Query: 用于数据获取和缓存管理

## 工具库

- VueUse: 提供常用的Vue组合式API工具集
- unplugin-auto-import: 自动导入API，减少重复import语句
- unplugin-icons: 图标管理
- unplugin-vue-components: 自动导入组件

# 代码规范与工具

## 代码格式化

- Prettier: 代码格式化工具
  - 使用单引号
  - 不使用分号
  - 行宽100字符
  - Vue文件中缩进script和style
  - 每行单个属性
  - 箭头函数总是使用括号
