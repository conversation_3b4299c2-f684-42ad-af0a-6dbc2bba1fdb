<template>
    <div :class="type == 'view'?'quetionItem1':'quetionItem'" v-for="(item, index) in questionLists" @mouseenter="showActions(item)"
        @mouseleave="hideActions(item)">
        <div class="flex items-center justify-between">
            <div class="flex items-start w-[75%]">
                <div>
                    <a-checkbox v-model:checked="item.checked" v-if="type != 'view'" @change="changeCheckbox(item)"></a-checkbox>
                </div>
                <div v-if="type == 'view'" style="font-weight: 500;">{{ index + 1 }}.</div>
                <div class="quetiontype  shrink-[0]">【{{ item.type }}】</div>
                <!-- <div class="content" >{{ item.stem }}</div> -->
                <div class="content" v-html="parseMarkdown(item.stem)"></div>
                 
            </div>

            <!-- -->
            <div class="flex justify-between h-[22px]" v-if="item == isShow && chooseType == 'quetion'" >
                <a-button type="text" size="small">
                    <div class="flex items-center">
                        <img src="@/assets/image/img/knowledgeicon.png" alt="" class="w-[14px] h-[14px]">
                        <div class="ml-[3] text-[#3F8CFF]">修改关联知识点</div>
                    </div>
                </a-button>
                <a-button type="text" size="small" @click="EditQuetion(item)">
                    <div class="flex items-center">
                        <img src="@/assets/image/img/edittable.png" alt="" class="w-[14px] h-[14px]">
                        <div class="ml-[3] text-[#3F8CFF]">编辑</div>
                    </div>
                </a-button>
                <a-button type="text" size="small" @click="deleteQuestion(item.id)">
                    <div class="flex items-center">
                        <img src="@/assets/image/img/blueDel.png" alt="" class="w-[14px] h-[14px]">
                        <div class="ml-[3] text-[#3F8CFF]">删除</div>
                    </div>
                </a-button>
            </div>
        </div>

        <div v-if="isOpen">
            <div :class="type == 'view' ? '' : 'ml-24'" class="remark flex gap-6">
                <div>创建时间: {{ formatDate(item.updated_at) }}</div>
                <div>归属人:{{ item.author }}</div>
                <div>难度:{{ levelTransform(item.difficulty) }} </div>
            </div>
            <!-- 选项 -->
            <div :class="type == 'view' ? '' : 'ml-24'" class="options">
                <div class="flex items-start" v-if="item.type == '单选题'||item.type == '判断题'">
                    <a-radio-group v-model:value="item.answer">
                        <a-radio :style="radioStyle" :value="it.key" v-for="(it, indx) in item.options" :key="index">
                            {{ it.key }}.
                            {{ it.value }}
                        </a-radio>
                    </a-radio-group>
                </div>
                <div class="flex items-start" v-if="item.type == '多选题'">
                    <a-checkbox-group v-model:value="item.answer" class="flex flex-col">
                        <a-checkbox :style="radioStyle" :value="it.key" v-for="(it, indx) in item.options" :key="index">
                            {{ it.key }}.
                            {{ it.value }}
                        </a-checkbox>
                    </a-checkbox-group>
                </div>
                
                
            </div>
            <!-- 答案 -->
            <div class="answer flex justify-between">
                <div class="flex flex-col">
                    <div class="ans h-[24px]" v-if="item.type == '填空题'">
                        答案：{{ formatAsString(item.answer) }}
                    </div>
                    <div v-else>
                        <div class="ans" v-if="Array.isArray(item.answer)">答案：{{ item.answer.join('、') }}</div>
                        <div class="ans" v-else>答案：{{ item.answer}}</div>
                    </div>
                    <div class="flex mt-[5px]">
                        <div class="ansy text-[#666666] mr-[7px]">解析说明：</div>
                        <div class="ancontent">
                            {{ item.explanation }}
                        </div>
                    </div>
                </div>
                
                <div class="shrink-0 flex" v-if="type == 'view'">
                    <a-divider type="vertical"  style="height: 84px; background-color: #E5E5E5;margin: 0 30px;" />
                    <div class="flex flex-col justify-center">
                        <div class="flex items-center">
                            分值：
                            <a-input-number id="inputNumber" v-model:value="item.score" 
                             @change="(value) => handleChangeScore(item, value)"
                            :min="1" style="width: 71px;height: 32px;" />
                        </div>
                        <div class="flex items-center mt-[10px] cursor-pointer" @click="deleteHomework(item)">
                            <IconTrash/>
                            <div class="text-[#333] leading-[20px] text-[14px] ml-[8px]">删除本题</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>
<script setup lang="ts">
 import IconTrash from '~icons/lucide/trash-2'
import { reactive, ref, watch } from 'vue'
import { parseMarkdown } from '@/utils/markdownParser'; // 引入工具函数
import { deleteQuestions } from '@/services/api/question';
import { message } from 'ant-design-vue'
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { formatDate, levelTransform } from '@/utils/util'
import { defineProps } from 'vue'
const emit = defineEmits(['deleteQue','editQue','chooseQue','deleteHomework','handleScore'])
interface QuestionItem {
    id: number;
    type: string;
    stem: string;
    updated_at: string;
    author: string; 
    difficulty: number;
    answer: string | string[];
    options: Array<{
        key: string;
        value: string;
    }>;
    explanation: string;
    checked: boolean;
    score: number;//分数
}

const props = defineProps({
    questionLists: {
        type: Array as () => QuestionItem[],
        default: () => []
    },//试题目列表
    isOpen: {
        type: Boolean,
        default: true
    },
    type: {
        type: String,
        default: '',//view为查看模式，选择完成加入作业中的显示
    },
    chooseType: {
        type: String,
        default: '',//选择模式，选择模式下，选择完成后加入作业
    }
})

const value = ref('1')
// align-items: ;
const radioStyle = reactive({
    display: 'flex',
    //   alignItems: 'flex-start',
    // //   height: '30px',
    lineHeight: '30px',
});


const isShow = ref() // 控制编辑按钮的显示
function showActions(item: any) {
    isShow.value = item
}
function hideActions(item: any) {
    isShow.value = null
}

//删除题目单一
async function deleteQuestion(id: any) {
    const confirmed = await showDeleteConfirm('确定删除本记录吗？删除后无法恢复！');
    if (confirmed) {
        deleteQuestions({ id: id }).then((res:any) => {
            // console.log(res);
            if (res.code == 200) {
                emit('deleteQue')
                message.success('删除成功')
            }
        })
    }
}
//编辑题目
function EditQuetion(item: any) {
    emit('editQue', item)
}

//转化填空题答案
const formatAsString = (items:any) => {
  return items.map((item:any) => item.blank).join(",");
};


//选择试题
function changeCheckbox(params: any){
    emit('chooseQue',params)
    if(props.chooseType == 'homework'){
        // emit('chooseQue',params)
    }
}

//删除试题从作业中
function deleteHomework(item: any){
    console.log(item)
    emit('deleteHomework',item)
}
function handleChangeScore(item: any, value: number) {
    item.score = value;
    emit('handleScore', item); // 向父组件传递更新后的 item
}

</script>
<style scoped lang="scss">
.quetionItem {
    // margin-left: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: #E5E5E5 1px solid;
}
.quetionItem1 {
    padding-top: 30px;
}

.quetiontype {
    color: #3F8CFF;
    /** 文本1 */
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 22.4px;
}

.content {
    /** 文本2 */
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 22.4px;
    color: #333333;
}

.remark {
    /** 文本1 */
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 12px;
    color: #999999;
    margin-top: 10px;
    // margin-left: 24px;
}

.ml-24 {
    margin-left: 24px;
}

.options {
    // margin-left: 24px;
    margin-top: 20px;
}

.answer {
    margin-top: 20px;
    border-radius: 4px;
    background: rgba(250, 250, 250, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    // height: 119.15px;
    padding: 20px 25px 15px 18px;

    .ans {
        color: #33C4A5;
        font-size: 14px;
    }

    .ansy {
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0px;
        flex-shrink: 0;
    }

    .ancontent {
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0px;
        // line-height: 14px;
        color: rgba(153, 153, 153, 1);

    }
}
</style>