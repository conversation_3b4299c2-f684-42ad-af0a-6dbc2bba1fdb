<template>
    <div class="">

        <div>
            <div class="flex">
                <div class="shrink-[0] text-[#000] text-[14px] w-[69px] text-right 
                    relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-4 before:text-[#FF4D4F]">
                    题干：
                </div>
                <QuestionEditor :questionValue="params.stem" :indexNumber="'text'" @valueEditorChange="getText" :key="params.id || params.aiid"/>
            </div>
            
            <div class="flex my-[17px]">
                <div class="shrink-[0] text-[#333] text-[14px] w-[69px] text-right relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-4 before:text-[#FF4D4F]">答案：</div>
                <div class="w-[100%]">
                    <a-textarea v-model:value="params.answer" placeholder=""  :rows="5" />
                </div>
            </div>
            

            <div class="flex items-center ">
                <div class="shrink-[0] text-[#333] text-[14px] w-[69px] text-right relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-4 before:text-[#FF4D4F]">解析：</div>
                <a-input v-model:value="params.explanation" placeholder="" />
            </div>
            <div class="flex items-center mt-[20px]">
                <div class="shrink-[0] text-[#000] text-[14px] w-[69px] relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-[-9px] before:text-[#FF4D4F]">难易程度：</div>
                <a-radio-group v-model:value="params.difficulty" name="radioGroup">
                    <a-radio :value="0" style="color:  rgba(0, 0, 0, 0.65);">简单</a-radio>
                    <a-radio :value="1" style="margin: 0 46px;color:  rgba(0, 0, 0, 0.65);">中等</a-radio>
                    <a-radio :value="2" style="color:  rgba(0, 0, 0, 0.65);">困难</a-radio>
                </a-radio-group>
            </div>
        </div>

        <div class="mt-[36px] flex justify-end gap-[8px]">
            <a-button class="outline-a-button-modal" @click="closeModal"> 取消 </a-button>
            <AButton
                type="primary"
                class="gradient-a-button-modal"
                @click="saveQuestion"
                :loading="loadingButton"
                >
                确定
            </AButton>
            <!-- <a-button style="color: #666;" @click="closeModal"> 取消 </a-button>
            <a-button type="primary" @click="saveQuestion" :loading="loadingButton" >确定</a-button> -->
        </div>

    </div>
</template>
<script lang="ts" setup>
import QuestionEditor from "@/components/QuestionEditor.vue";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { v4 as uuidv4 } from 'uuid'; // 导入uuid库
import { addAnswerQuestions, editAnswerQuestions } from '@/services/api/question'
import { openMessage,getEn } from '@/utils/util'
import { useRouter } from 'vue-router';
const emit = defineEmits(['closeModal']);
import { message } from 'ant-design-vue';
const loadingButton = ref(false);

const router = useRouter();
// 定义数据类型
interface EditorData {
    indexNumber: string | number;
    valueEditor: string;
}

const props = defineProps({
    questionType: {
        type: String,
        required: true
    },
    questionDetails: {
        type: Object,
        required: true
    },
    isType: {
        tye: String,
        required: false
    },
    editType: { // 编辑类型是从AI传过来  还是从试题过来编辑
        type: String,
        required: true
    }
})


// 默认值
const defaultParams = {
    id:'',
    aiid:'',
    type: props.questionType,
    stem: "",
    difficulty: 0,  // 难度
    knowledge_point: '',  // 知识点
    label_id: 1,  // 标签
    answer: "", //答案，
    // judge_point:[
    //     {
    //         "content":"", //判分内容
    //         "score":1 //分数，若不设置该字段数值，系统不会设置默认分数
    //     }
    // ], // 判分点，"content":判分内容, "score":该判分内容的分值
    explanation: '', // 解析
    score_mode:"human",  //得分设置，("human", "人工判分"), ("keywords", "按关键字自动判分"), ('ai', 'AI阅卷判分')，默认是human
    total_score:1  //可获得分数
};

// 响应式对象
const params = ref({ ...defaultParams });

onMounted(() => {
    if (props.questionDetails) {
        params.value = { ...defaultParams, ...props.questionDetails }; // 深度合并默认值和传入的值
        console.log(params.value, 'params.value');
    }
})

watch(() => props.questionDetails, (newValue, oldValue) => {
    if (newValue) {
        params.value = { ...defaultParams, ...newValue }; // 深度合并默认值和传入的值
        console.log(params.value, 'params.value');
    }
});

//获取题干文本数据
function getText(data: EditorData) {
    if (data.indexNumber == 'text') {
        params.value.stem = data.valueEditor
    } else if (typeof data.indexNumber == 'number') {
        // params.value.answer[data.indexNumber].blank = `${data.valueEditor}`// ${getEn(data.indexNumber)}.${' '}
    } else if (data.indexNumber == 'explanation') {
        params.value.explanation = data.valueEditor
    } else if (data.indexNumber == 'answer') {
        params.value.answer = data.valueEditor
    }
}


//保存
function saveQuestion() {
    
    // const optionsToSend = params.value.options.map((option,index) => `${getEn(index)}.${' '}${option.value}`);
    // const hasEmptyOption = params.value.judge_point.some(option => option.content.trim() === '');
    

    if (params.value.stem == '') {
        message.warn('请输入题干')
        return
    }
    // if (hasEmptyOption) {
    //     openMessage('请输入全部判分内容', 'error');
    //     return;
    // }
    if (params.value.answer == '') {
        message.warn('请输入答案')
        return
    }
    if (params.value.explanation == '') {
        message.warn('请输入解析')
        return
    }
    if(params.value.difficulty === null || params.value.difficulty === undefined){
        message.warn('请选择难易程度')
        return
    }

    //修改AI生成试题传过来的试题
    if(props.editType == 'isAiEdit'){
        emitter.emit('changeQuestion', params.value);
        return
    }

    console.log(params.value, 'params.value');
    loadingButton.value = true;

    // return
    if(props.isType == 'edit'){
        console.log('修改试题')
        editAnswerQuestions(params.value).then(res => {
            console.log(res);
            loadingButton.value = false;
            emit('closeModal')
            // openMessage('修改成功', 'success')
            // setTimeout(() => {
            //     resetForm();
            //     window.location.reload(); 
            // },1000)   // 刷新页面
        }).catch(err => {
            loadingButton.value = false;
            openMessage('保存失败', 'error')
        })
    }else if(props.isType == 'add'){
        console.log('新增试题')
        addAnswerQuestions(params.value).then(res => {
            // console.log(res, 'res');
            openMessage('保存成功', 'success')
            loadingButton.value = false;
            emit('closeModal')
            setTimeout(() => {
                resetForm();
            },1000)   // 刷新页面
        })
    }

}
function resetForm() {
    params.value.type= props.questionType,
    params.value.stem = ''
    params.value.difficulty = 0
    params.value.knowledge_point = ''
    params.value.label_id = 1
    params.value.answer = "",

    params.value.explanation = '',
    params.value.score_mode = "human",  //得分设置，("partial", "按空得分累加"), ("all", "全部正确得分")，默认是partial
    params.value.total_score = 1 //可获得分数
}


function closeModal(){
    console.log('关闭')
    emit('closeModal')
    if(props.editType == 'isAiEdit'){
        return
    }
    params.value = { ...defaultParams };
}
</script>
<style lang="scss" scoped>
// @import url('@/assets/css/questioncss/selectCss.css');

// ::v-deep(.el-input-number .el-input__inner){
//     text-align: left !important;
// }
</style>