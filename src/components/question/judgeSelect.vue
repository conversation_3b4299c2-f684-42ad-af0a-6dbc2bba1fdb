<template>
    <div class="">
        <div>
            <div class="flex">
                <div class="shrink-[0] text-[#000] text-[14px] w-[69px] text-right 
                    relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-4 before:text-[#FF4D4F]">
                    题干：
                </div>
                <QuestionEditor :questionValue="params.stem" :indexNumber="'text'" @valueEditorChange="getText" :key="params.id || params.aiid" />
            </div>
            <div v-for="(item, index) in params.options" :key="index" class="flex mt-[17px] items-center">
                <div class="w-[69px] text-right shrink-[0] text-[#333] pr-[6px]
                    relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-6 before:text-[#FF4D4F]">
                    {{ getEn(index) }}：
                </div>
                <a-input v-model:value="item.value" placeholder="" />
                <MinusSquareOutlined class="mx-[20px]"  @click="deleteQuestion(index,getEn(index))"/>
                <div class="w-[101px]">
                    <a-button v-if="getEn(index) == params.answer" style="color: #3F8CFF;border:1px solid #3F8CFF;background-color: #DBEAFF;width: 101px;" @click="setAnswer(getEn(index))">答案选项</a-button>
                    <a-button v-else style="color: #666;width: 101px;" @click="setAnswer(getEn(index))">设置为答案</a-button>
                </div>
            </div>
            <!-- <div class="my-[17px] ml-[69px]">
                <a-button style="color: #666;" @click="addQuestion">
                    添加选项
                </a-button>
            </div> -->
            <div class="flex items-center mt-[17px]">
                <div class="shrink-[0] text-[#333] text-[14px] w-[69px] text-right relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-4 before:text-[#FF4D4F]">解析：</div>
                <a-input v-model:value="params.explanation" placeholder="" />
            </div>
            <div class="flex items-center mt-[20px]">
                <div class="shrink-[0] text-[#000] text-[14px] w-[69px] relative before:content-['*'] before:size-[5px] before:absolute before:top-0.5 before:left-[-9px] before:text-[#FF4D4F]">难易程度：</div>
                <a-radio-group v-model:value="params.difficulty" name="radioGroup">
                    <a-radio :value="0" style="color:  rgba(0, 0, 0, 0.65);">简单</a-radio>
                    <a-radio :value="1" style="margin: 0 46px;color:  rgba(0, 0, 0, 0.65);">中等</a-radio>
                    <a-radio :value="2" style="color:  rgba(0, 0, 0, 0.65);">困难</a-radio>
                </a-radio-group>
            </div>
        </div>

        <div class="mt-[36px] flex justify-end gap-[8px]">
            <!-- <a-button style="color: #666;" @click="closeModal"> 取消 </a-button>
            <a-button type="primary" @click="saveQuestion" :loading="loadingButton" >确定</a-button> -->
            <a-button class="outline-a-button-modal" @click="closeModal"> 取消 </a-button>
            <AButton
                type="primary"
                class="gradient-a-button-modal"
                @click="saveQuestion"
                :loading="loadingButton"
                >
                确定
            </AButton>
        </div>

    </div>
</template>
<script lang="ts" setup>
import { MinusSquareOutlined } from '@ant-design/icons-vue'
import QuestionEditor from "@/components/QuestionEditor.vue";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { v4 as uuidv4 } from 'uuid'; // 导入uuid库
import { addJudgeQuestions, editJudgeQuestions } from '@/services/api/question'
import { openMessage,getEn } from '@/utils/util'
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
const emit = defineEmits(['closeModal']);
const loadingButton = ref(false)

const router = useRouter();
// 定义数据类型
interface EditorData {
    indexNumber: string | number;
    valueEditor: string;
}

const props = defineProps({
    questionType: {
        type: String,
        required: true
    },
    questionDetails: {
        type: Object,
        required: true
    },
    isType: {
        tye: String,
        required: false
    },
    editType: { // 编辑类型是从AI传过来  还是从试题过来编辑
        type: String,
        required: true
    }
})
//设置答案
function setAnswer(data: any) {
    console.log(data,'data')
    params.value.answer = data
}



// 默认值
const defaultParams = {
    id:'',
    aiid:'',
    type: props.questionType,
    stem: "",
    difficulty: 0,  // 难度
    knowledge_point: '',  // 知识点
    label_id: 1,  // 标签
    options: [
        { value: "", key: '' },
        { value: "", key: '' },
    ],
    answer: "",
    explanation: '' // 解析
};

// 响应式对象
const params = ref({ ...defaultParams });

onMounted(() => {
    if (props.questionDetails) {
        params.value = { ...defaultParams, ...props.questionDetails }; // 深度合并默认值和传入的值
    }
})
watch(() => props.questionDetails, (newValue, oldValue) => {
    if (newValue) {
        params.value = { ...defaultParams, ...newValue }; // 深度合并默认值和传入的值
    }
});

function addQuestion() {
    params.value.options.push({ value: "", key: '' })
}
//获取题干文本数据
function getText(data: EditorData) {
    if (data.indexNumber == 'text') {
        params.value.stem = data.valueEditor
    } else if (typeof data.indexNumber == 'number') {
        params.value.options[data.indexNumber].value = `${data.valueEditor}`// ${getEn(data.indexNumber)}.${' '}
    } else if (data.indexNumber == 'explanation') {
        params.value.explanation = data.valueEditor
    }
}

//删除选项
function deleteQuestion(index: number,answer: string) {
    message.warn('判断题不支持删除')
    return
    params.value.options.splice(index, 1);
}



//保存
function saveQuestion() {
    // const optionsToSend = params.value.options.map((option,index) => `${getEn(index)}.${' '}${option.value}`);
    // const hasEmptyOption = params.value.options.some(option => option.value.trim() === '');
    const hasEmptyOption = params.value.options.some(option => 
        !(option?.value ?? '').trim()
    );
    console.log(hasEmptyOption,'hasEmptyOption')
    params.value.options = params.value.options.map((option,index) => {
        return {
            key: getEn(index),
            value: option.value
        };
    })

    if (params.value.stem == '') {
        message.warn('请输入题干')
        return
    }
    if (hasEmptyOption) {
        message.warn('请输入全部选项');
        return;
    }
    if (params.value.answer == '') {
        message.warn('请选择答案')
        return
    }
    if(params.value.difficulty === null || params.value.difficulty === undefined){
        message.warn('请选择难易程度')
        return
    }

    //修改AI生成试题传过来的试题
    if(props.editType == 'isAiEdit'){
        emitter.emit('changeQuestion', params.value);
        return
    }
    console.log(params.value, 'params.value');
    loadingButton.value = true
    // return
    
    if(props.isType == 'edit'){
        console.log('修改试题')
        editJudgeQuestions(params.value).then(res => {
            console.log(res);
            loadingButton.value = false
            emit('closeModal')
            openMessage('修改成功', 'success')
            // setTimeout(() => {
            //     resetForm();
            //     window.location.reload(); 
            // },1000)   // 刷新页面
        })
    }else if(props.isType == 'add'){
        console.log('新增试题')
        addJudgeQuestions(params.value).then(res => {
            // console.log(res, 'res');
            openMessage('保存成功', 'success')
            emit('closeModal')
            loadingButton.value = false
            setTimeout(() => {
                resetForm();
                // window.location.reload(); 
            },1000)   // 刷新页面
        })
    }

}
function resetForm() {
    params.value.stem = ''
    params.value.difficulty = 0
    params.value.knowledge_point = ''
    params.value.label_id = 1
    params.value.options = [
        { value: "", key: '' },
        { value: "", key: '' },
    ]
    params.value.answer = ''
    params.value.explanation = ''
}

function closeModal(){
    console.log('关闭')
    emit('closeModal')
    if(props.editType == 'isAiEdit'){
        return
    }
    params.value = { ...defaultParams };
}
</script>
<style lang="scss" scoped>
// @import url('@/assets/css/questioncss/selectCss.css');
</style>