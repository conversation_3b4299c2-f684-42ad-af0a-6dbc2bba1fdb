<template>
    <div class="quetionItem" v-for="item in props.itemQuetione" @mouseenter="showActions(item.aiid)" @mouseleave="hideActions(item.aiid)">
        <div class="">
            <div class="flex justify-between align-center">
                <div class="quetiontype flex justify-center align-center mb-[10]">{{item.type}}</div>
                <div v-if="item.aiid == isShow">
                    <a-button type="text" size="small" @click="editQuetion(item)">
                         <!-- -->
                        <div class="flex items-center">
                            <img src="@/assets/image/img/edittable.png" alt="" class="w-[14px] h-[14px]">
                            <div class="ml-[3] text-[#3F8CFF]">编辑</div>
                        </div>
                    </a-button>
                    <a-button type="text" size="small" @click="deleteQuestion(item)">
                        <div class="flex items-center">
                            <img src="@/assets/image/img/reddel.png" alt="" class="w-[14px] h-[14px]">
                            <div class="ml-[3] text-[#FA0000]">删除</div>
                        </div>
                    </a-button>
                </div>
            </div>
            <div class="content">{{ item.stem }}</div>
        </div>
        <!-- 选项 -->
        <div v-if="isOpen">
            <div class="flex align-center mt-[10]" v-if="item.type == '单选题'||item.type == '判断题'">
                <a-radio-group v-model:value="item.answer">
                    <a-radio :style="radioStyle" :value="it.key" v-for="(it,idx) in item.options" :key="it.key" >
                        {{ getEn(idx) }}
                        {{ it.value }}</a-radio>
                </a-radio-group>
            </div>

            <div class="flex items-start mt-[10]" v-if="item.type == '多选题'">
                <a-checkbox-group v-model:value="item.answer" class="flex flex-col">
                    <a-checkbox :style="radioStyle" :value="it.key" v-for="(it, indx) in item.options" :key="it">
                        {{ it.key }}.
                        {{ it.value }}
                    </a-checkbox>
                </a-checkbox-group>
            </div>
        </div>

        <!-- 简答题或者填空题答案 -->
         <div v-if="item.type == '填空题'" class="flex text-[#333333] text-[14px] font-medium  mt-[11]">
            <div class="shrink-[0]">答案：</div>
            <div v-for="(ans, indexans) in item.answer">
                {{ ans.blank }}、
            </div>
         </div>
         <div v-if="item.type == '问答题'" class="flex text-[#333333] text-[14px] font-medium  mt-[11]">
            <div class="shrink-[0]">答案：</div>
            <div>
                {{ item.answer }}
            </div>
         </div>

        <!-- <div :class="type == 'view' ? '' : 'ml-24'" class="options">
                <div class="flex items-start" v-if="item.type == '单选题'||item.type == '判断题'">
                    <a-radio-group v-model:value="item.answer">
                        <a-radio :style="radioStyle" :value="it.key" v-for="(it, indx) in item.options" :key="index">
                            {{ it.key }}.
                            {{ it.value }}
                        </a-radio>
                    </a-radio-group>
                </div>
                <div class="flex items-start" v-if="item.type == '多选题'">
                    <a-checkbox-group v-model:value="item.answer" class="flex flex-col">
                        <a-checkbox :style="radioStyle" :value="it.key" v-for="(it, indx) in item.options" :key="index">
                            {{ it.key }}.
                            {{ it.value }}
                        </a-checkbox>
                    </a-checkbox-group>
                </div>
                
                
            </div> -->
        
        <!-- 答案 -->
        <div class="answer" v-if="isOpen">
            <div class="ansy">解析说明：</div>
            <div class="ancontent">
                {{ item.explanation }}
            </div>
        </div>

        
    </div>
</template>
<script setup lang="ts">
const Ens = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"];
// 根据索引获取字母
const getEn = (index: number) => {
    return Ens[index] || ""; // 如果索引超出范围，返回空字符串
};
import { defineProps } from 'vue'
const emit = defineEmits(['deleteQue','editQue'])
const props = defineProps({
    // url: {
    //     type: String,
    //     default: ''
    // }
    itemQuetione: {
        type: Object,
        default: () => { }
    },
    isOpen: {
        type: Boolean,
        default: true
    },
    type: {
        type: String,
        default: '',//view为查看模式，
    }
})
const checked = ref(false)
const value = ref('1')
const radioStyle = reactive({
    display: 'flex',
    lineHeight: '30px',
});

const isShow = ref() // 控制编辑按钮的显示
function showActions(item:any) {
    isShow.value = item
}
function hideActions(item:any) {
    isShow.value = null
}

// const showEdit = ref(false)
// const quetionDetails = ref() //编辑试题的数据
function editQuetion(item: any) {
    // console.log('编辑',item);
    // quetionDetails.value = item
    // showEdit.value = true
    emit('editQue',item)
}
function deleteQuestion(item: any) {
    emit('deleteQue',item)
}

</script>
<style scoped lang="scss">
// ::v-deep .ant-radio-disabled .ant-radio-inner {
//   background-color: #fff;
//   border-color: #3F8CFF !important;
// }

// ::v-deep .ant-radio-disabled .ant-radio-inner::after {
//   background-color: rgba(0, 0, 0, 0.2);
// }

::v-deep .ant-radio-disabled + span {
  color: rgba(51, 51, 51, 1);
}

.quetionItem {
    margin-top: 20px;
    padding: 20px;
    border-radius: 5px;
    background: rgba(228, 236, 242, 0.2);
}

.quetiontype {
    color: #3F8CFF;
    /** 文本1 */
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 22.4px;
    background: rgba(63, 140, 255, 0.2);
    width: 62px;
    border-radius: 2px;
}

.content {
    /** 文本2 */
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 22.4px;
    color: #333333;
}

.remark {
    /** 文本1 */
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 12px;
    color: #999999;
    margin-top: 10px;
    // margin-left: 24px;
}

.ml-24 {
    margin-left: 24px;
}

.options {
    // margin-left: 24px;
    margin-top: 10px;
}

.answer {
    margin-top: 20px;
    border-radius: 4px;
    background: rgba(250, 250, 250, 1);
    border-top: 1px solid rgba(232, 232, 232, 1);
    padding-top: 16px;

    .ans {
        color: #33C4A5;
        font-size: 14px;
    }

    .ansy {
        width: 88px;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0px;
        flex-shrink: 0;
    }

    .ancontent {
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0px;
        // line-height: 14px;
        color: rgba(153, 153, 153, 1);

    }
}
</style>