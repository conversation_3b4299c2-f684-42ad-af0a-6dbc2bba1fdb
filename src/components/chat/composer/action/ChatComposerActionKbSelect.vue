<script setup lang="ts">
  import type { CascaderOptionType } from 'ant-design-vue/es/cascader'

  import {
    injectExtraComposerContext,
    type ChatCourseAttachment,
    type ChatKbAttachment,
  } from '../ChatComposer.vue'

  import IconCaretDown from '~icons/ant-design/caret-down-filled'
  import IconChevronRight from '~icons/lucide/chevron-right'
  import IconX from '~icons/material-symbols/cancel-rounded'

  const { attachments } = injectExtraComposerContext()

  const courses = ref<ChatCourseAttachment[]>([{ type: 'course', name: '大学物理', id: '1' }])
  const kbs = ref<ChatKbAttachment[]>([{ type: 'kb', name: '大学物理知识库', id: '2' }])

  const courseOptions = reactiveComputed(() =>
    courses.value.map((course) => ({
      value: course.id,
      label: course.name,
    })),
  )
  const kbOptions = reactiveComputed(() =>
    kbs.value.map((kb) => ({
      value: kb.id,
      label: kb.name,
    })),
  )

  const options: CascaderOptionType[] = [
    {
      value: 'course',
      label: '课程',
      children: courseOptions,
      isLeaf: false,
    },
    {
      value: 'kb',
      label: '知识库',
      children: kbOptions,
      isLeaf: false,
    },
  ]

  const cascaderValue = ref<string[]>()
  watch(cascaderValue, (values) => {
    if (values === undefined) {
      return
    }

    if (values.length !== 2) {
      throw new Error('Cascader malfunctioning')
    }

    const [type, id] = values
    if (type === 'course') {
      const course = courses.value.find((course) => course.id === id)!
      if (course) {
        attachments.value = [course]
      }
    } else if (type === 'kb') {
      const kb = kbs.value.find((kb) => kb.id === id)!
      if (kb) {
        attachments.value = [kb]
      }
    }
  })
  watch(attachments, (attachments) => {
    const attachment = attachments[0]
    if (attachment === undefined || attachment.type === 'file') {
      cascaderValue.value = undefined
    }
  })
</script>

<template>
  <ACascader
    v-model:value="cascaderValue"
    :display-render="({ labels }) => labels[0]"
    :options="options"
    :disabled="attachments.length > 0"
    expand-trigger="hover"
    placeholder="类别"
    class="reset-ant-cascader w-[100px]!"
  >
    <template #expandIcon>
      <IconChevronRight class="text-foreground-3" />
    </template>
    <template #suffixIcon>
      <IconCaretDown class="text-foreground-3" />
    </template>
    <template #clearIcon>
      <IconX class="-translate-x-1 -translate-y-px" />
    </template>
  </ACascader>
</template>

<style scoped>
  @reference "tailwindcss";

  .reset-ant-cascader {
    &:deep() {
      .ant-select-selector {
        @apply rounded-full pr-3 pl-4.5;
      }
    }
  }
</style>
