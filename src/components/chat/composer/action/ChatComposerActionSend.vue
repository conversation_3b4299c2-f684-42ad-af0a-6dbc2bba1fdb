<script setup lang="ts">
  import { ComposerActionSend } from '@wicii/chat-primitive'
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  import IconPlane from '~icons/local/paper-plane'

  const props = defineProps<{ class?: HTMLAttributes['class'] }>()
</script>

<template>
  <ComposerActionSend as-child>
    <AButton
      type="primary"
      html-type="submit"
      :class="twMerge('gradient-a-button rounded-full! px-4! transition-none!', props.class)"
    >
      <IconPlane class="-ml-0.5 size-4.5" />
    </AButton>
  </ComposerActionSend>
</template>
