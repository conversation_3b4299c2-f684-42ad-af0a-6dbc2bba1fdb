<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  import IconX from '~icons/material-symbols/cancel-rounded'

  export type Props = {
    class?: HTMLAttributes['class']
    name: string
    wordCount?: number
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{ remove: [] }>()

  const filenameParts = computed(() => getFilenameParts(props.name))
  const noInfo = computed(() => filenameParts.value.ext === '' && props.wordCount === undefined)
</script>

<template>
  <div
    :class="
      twMerge(
        'text-foreground-3 relative w-max rounded-[5px] bg-[rgba(71,148,254,0.05)] py-2 pr-3.5 pl-2.5',
        props.class,
      )
    "
  >
    <div class="flex space-x-2.5">
      <FileTypeImg :file-name="props.name" />
      <div :class="twMerge('max-w-[160px] space-y-1', noInfo && 'flex items-center')">
        <TextEllipsis
          :text="filenameParts.name"
          :lines="1"
          :tooltip="{ title: props.name, placement: 'top' }"
          :class="twMerge('text-xs', noInfo && 'text-sm')"
        />

        <div
          class="flex space-x-2.5 text-[10px]"
          v-if="noInfo === false"
        >
          <div>
            {{ filenameParts.ext }}
          </div>
          <div v-if="props.wordCount !== undefined">{{ props.wordCount }}字</div>
        </div>
      </div>
    </div>

    <button
      class="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 cursor-pointer"
      @click="emit('remove')"
    >
      <IconX class="text-foreground-3 hover:text-foreground-3/80" />
    </button>
  </div>
</template>
