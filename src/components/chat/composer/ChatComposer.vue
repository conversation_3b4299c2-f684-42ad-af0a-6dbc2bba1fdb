<script lang="ts">
  export type ChatFileAttachment = {
    type: 'file'
    name: string
    wordCount?: number
  }

  export type ChatKbAttachment = {
    type: 'kb'
    name: string
    id: string
  }

  export type ChatCourseAttachment = {
    type: 'course'
    name: string
    id: string
  }

  export type ChatAttachment = ChatFileAttachment | ChatKbAttachment | ChatCourseAttachment

  const extraComposerContextInjectionKey = Symbol('chat.composer') as InjectionKey<{
    attachments: Ref<ChatAttachment[]>
  }>

  export function injectExtraComposerContext() {
    const context = inject(extraComposerContextInjectionKey)
    if (!context) {
      throw new Error('extraComposerContext is not provided')
    }
    return context
  }
</script>

<script setup lang="ts">
  import { ComposerForm, injectChatContext } from '@wicii/chat-primitive'

  const { status } = injectChatContext()

  const attachments = ref<ChatAttachment[]>([])
  provide(extraComposerContextInjectionKey, { attachments })

  watch(attachments, console.log)
</script>

<template>
  <ComposerForm class="composer-wrapper space-y-2 rounded-[10px] p-4">
    <div
      class="flex flex-wrap gap-5"
      v-if="attachments.length > 0"
    >
      <template
        v-for="(attachment, index) in attachments"
        :key="`${index}-${attachment.type}-${attachment.name}`"
      >
        <ChatComposerAttachmentFile
          v-if="attachment.type === 'file'"
          :name="attachment.name"
          :word-count="attachment.wordCount"
          @remove="attachments.splice(index, 1)"
        />
        <ChatComposerAttachmentKb
          v-else-if="attachment.type === 'kb'"
          :name="attachment.name"
          @remove="attachments.splice(index, 1)"
        />
        <ChatComposerAttachmentCourse
          v-else-if="attachment.type === 'course'"
          :name="attachment.name"
          @remove="attachments.splice(index, 1)"
        />
      </template>
    </div>

    <ChatComposerTextarea />

    <div class="flex">
      <ChatComposerActionKbSelect />

      <ChatComposerActionStop
        v-if="status === 'submitted' || status === 'streaming'"
        class="ml-auto"
      />
      <ChatComposerActionSend
        v-else
        class="ml-auto"
      />
    </div>
  </ComposerForm>
</template>

<style scoped>
  .composer-wrapper {
    --background-color: white;
    background: var(--background-color);
    border-width: 2px;
    border-color: var(--background-color);

    &:has(textarea:focus-visible) {
      border-color: transparent;
      background:
        linear-gradient(var(--background-color), var(--background-color)) padding-box,
        linear-gradient(145deg, rgba(73, 146, 255, 1), rgba(32, 195, 231, 1)) border-box;
    }
  }
</style>
