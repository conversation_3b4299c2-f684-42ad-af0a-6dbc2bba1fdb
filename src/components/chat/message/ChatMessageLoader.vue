<template>
  <div class="loader" />
</template>

<style scoped>
  /* HTML: <div class="loader"></div> */
  .loader {
    color: var(--color-primary);
    width: 4px;
    aspect-ratio: 1;
    border-radius: 50%;
    box-shadow:
      14px 0 0 3.5px,
      28px 0 0 1px,
      42px 0 0 0;
    transform: translateX(-38px);
    animation: l21 0.5s infinite alternate linear;
  }

  @keyframes l21 {
    50% {
      box-shadow:
        14px 0 0 1px,
        28px 0 0 3.5px,
        42px 0 0 1px;
    }
    100% {
      box-shadow:
        14px 0 0 0,
        28px 0 0 1px,
        42px 0 0 3.5px;
    }
  }
</style>
