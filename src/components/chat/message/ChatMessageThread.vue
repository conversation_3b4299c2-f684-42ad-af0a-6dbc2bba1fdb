<script setup lang="ts">
  import { injectChatContext, MessageRoot, ThreadRoot } from '@wicii/chat-primitive'

  const { messages } = injectChatContext()
</script>

<template>
  <ThreadRoot auto-scroll>
    <MessageRoot
      v-for="(message, index) in messages"
      :key="message.id"
      :message="message"
      :message-index="index"
    >
      <ChatUserMessage v-if="message.role === 'user'" />
      <ChatAssistantMessage v-else-if="message.role === 'assistant'" />
    </MessageRoot>
  </ThreadRoot>
</template>
