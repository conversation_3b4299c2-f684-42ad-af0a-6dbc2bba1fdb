<script setup lang="ts">
  import { injectMessageContext } from '@wicii/chat-primitive'

  import IconGPT from '~icons/arcticons/openai-chatgpt'

  const { message, phase } = injectMessageContext()
</script>

<template>
  <ChatContainer class="pb-7">
    <div class="flex space-x-2">
      <div class="pt-1 text-black">
        <IconGPT />
      </div>

      <div>
        <div class="flex items-center">
          DeepSeek V3
          <ChatAssistantLoader
            class="ml-10"
            v-if="phase !== 'complete'"
          />
        </div>
        <template
          v-for="(part, index) in message.parts"
          :key="index"
        >
          <ChatAssistantReasoningPart
            v-if="part.type === 'reasoning'"
            :text="part.reasoning"
          />
          <ChatAssistantTextPart
            v-else-if="part.type === 'text'"
            :text="part.text"
          />
        </template>
      </div>
    </div>
  </ChatContainer>
</template>
