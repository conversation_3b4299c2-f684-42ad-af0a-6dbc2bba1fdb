<script setup lang="ts">
  import type { HTMLAttributes } from 'vue'
  import { twMerge } from 'tailwind-merge'

  const props = defineProps<{ class?: HTMLAttributes['class'] }>()
</script>

<template>
  <div
    :class="
      twMerge(
        'mx-auto w-full px-5 md:max-w-[700px] lg:max-w-[820px] xl:max-w-[960px] 2xl:max-w-[1200px]',
        props.class,
      )
    "
  >
    <slot />
  </div>
</template>
