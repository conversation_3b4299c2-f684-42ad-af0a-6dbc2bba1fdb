<script setup lang="ts">
  import { VueMarkdown } from '@crazydos/vue-markdown'
  import remarkGfm from 'remark-gfm'

  interface Props {
    markdown: string
  }

  const props = defineProps<Props>()
</script>

<template>
  <VueMarkdown
    :markdown="props.markdown"
    :remark-plugins="[remarkGfm]"
  >
    <template #p="{ children }">
      <p class="my-2 leading-7 first:mt-0">
        <component :is="children" />
      </p>
    </template>

    <template #a="{ children }">
      <UButton
        variant="link"
        class="p-0 text-base"
      >
        <component :is="children" />
      </UButton>
    </template>

    <template #h1="{ children }">
      <h1 class="my-4 text-3xl font-extrabold tracking-tight lg:text-4xl">
        <component :is="children" />
      </h1>
    </template>

    <template #h2="{ children }">
      <h2 class="mt-4 mb-3 text-2xl font-bold tracking-tight">
        <component :is="children" />
      </h2>
    </template>

    <template #h3="{ children }">
      <h3 class="mt-4 mb-2 text-xl font-semibold tracking-tight">
        <component :is="children" />
      </h3>
    </template>

    <template #h4="{ children }">
      <h4 class="my-2 text-lg font-medium tracking-tight">
        <component :is="children" />
      </h4>
    </template>

    <template #hr>
      <USeparator class="my-8" />
    </template>

    <template #inline-code="{ children }">
      <code
        class="relative mx-[0.1rem] rounded bg-(--ui-bg-elevated) px-[0.3rem] py-[0.2rem] font-mono text-sm font-medium"
      >
        <component :is="children" />
      </code>
    </template>

    <template #li="{ children }">
      <li class="my-2">
        <component :is="children" />
      </li>
    </template>

    <template #ol="{ children }">
      <ol class="mb-4 list-decimal pl-6">
        <component :is="children" />
      </ol>
    </template>

    <template #ul="{ children }">
      <ul class="mb-4 list-disc pl-6">
        <component :is="children" />
      </ul>
    </template>

    <template #table="{ children }">
      <div class="my-6 overflow-auto text-sm **:border-(--ui-border-accented)">
        <table class="w-full min-w-max">
          <component :is="children" />
        </table>
      </div>
    </template>

    <template #thead="{ children }">
      <thead class="border-b">
        <component :is="children" />
      </thead>
    </template>

    <template #th="{ children }">
      <th
        class="px-4 py-3 text-left font-bold [&[align=center]]:text-center [&[align=right]]:text-right"
      >
        <component :is="children" />
      </th>
    </template>

    <template #td="{ children }">
      <td class="px-4 py-3 text-left [&[align=center]]:text-center [&[align=right]]:text-right">
        <component :is="children" />
      </td>
    </template>

    <template #tr="{ children }">
      <tr class="not-first:border-t">
        <component :is="children" />
      </tr>
    </template>

    <template #block-code="{ language, content }">
      <ChatMarkdownCodeBlock
        :language="language"
        :code="content"
      />
    </template>
  </VueMarkdown>
</template>
