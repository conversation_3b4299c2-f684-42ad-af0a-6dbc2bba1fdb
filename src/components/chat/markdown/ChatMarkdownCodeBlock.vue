<script lang="ts">
  import {
    createHighlighter,
    type Highlighter,
    type BundledLanguage,
    type StringLiteralUnion,
    type SpecialLanguage,
  } from 'shiki'
  import type { FunctionalComponent } from 'vue'

  import IconJs from '~icons/devicon/javascript'
  import IconJsx from '~icons/devicon/react'
  import IconJson from '~icons/devicon/json'
  import IconTs from '~icons/devicon/typescript'
  import IconTsx from '~icons/devicon/react'
  import IconVue from '~icons/devicon/vuejs'
  import IconCss from '~icons/devicon/css3'
  import IconHtml from '~icons/devicon/html5'
  import IconBash from '~icons/devicon/bash'
  import IconMd from '~icons/devicon/markdown'
  import IconYaml from '~icons/devicon/yaml'
  import IconC from '~icons/devicon/c'
  import IconCpp from '~icons/devicon/cplusplus'
  import IconPython from '~icons/devicon/python'
  import IconGo from '~icons/devicon/go'
  import IconJava from '~icons/devicon/java'
  import IconRuby from '~icons/devicon/ruby'
  import IconFish from '~icons/devicon/bash'
  import IconCSharp from '~icons/devicon/csharp'
  import IconZsh from '~icons/devicon/zsh'
  import IconRust from '~icons/devicon/rust'
  import IconText from '~icons/vscode-icons/file-type-text'
  import IconAngular from '~icons/devicon/angularjs'
  import IconSvelte from '~icons/devicon/svelte'
  import IconPhp from '~icons/devicon/php'
  import IconSwift from '~icons/devicon/swift'
  import IconDart from '~icons/devicon/dart'
  import IconKotlin from '~icons/devicon/kotlin'
  import IconScala from '~icons/devicon/scala'
  import IconElixir from '~icons/devicon/elixir'
  import IconHaskell from '~icons/devicon/haskell'
  import IconLua from '~icons/devicon/lua'
  import IconVim from '~icons/devicon/vim'

  let _globalHighlighter: Promise<Highlighter> | undefined = undefined

  export function getShikiHighlighter() {
    if (_globalHighlighter) {
      return _globalHighlighter
    }

    _globalHighlighter = createHighlighter({
      themes: ['catppuccin-latte', 'catppuccin-macchiato'],
      langs: [
        'js',
        'jsx',
        'json',
        'ts',
        'tsx',
        'vue',
        'css',
        'html',
        'bash',
        'md',
        'yaml',
        'c',
        'cpp',
        'python',
        'go',
        'java',
        'ruby',
        'fish',
        'csharp',
        'zsh',
        'rust',
        'text',
        'angular-ts',
        'angular-html',
        'svelte',
        'vimscript',
        'lua',
        'dart',
        'swift',
        'php',
        'kotlin',
        'elixir',
        'haskell',
        'scala',
      ],
    })

    return _globalHighlighter
  }

  type Langs = StringLiteralUnion<BundledLanguage> | SpecialLanguage
  export const languageIcons: Partial<Record<Langs, FunctionalComponent>> = {
    js: IconJs,
    javascript: IconJs,
    jsx: IconJsx,
    json: IconJson,
    json5: IconJson,
    jsonc: IconJson,
    ts: IconTs,
    typescript: IconTs,
    tsx: IconTsx,
    vue: IconVue,
    'vue-html': IconVue,
    css: IconCss,
    html: IconHtml,
    bash: IconBash,
    md: IconMd,
    markdown: IconMd,
    yaml: IconYaml,
    yml: IconYaml,
    c: IconC,
    cpp: IconCpp,
    python: IconPython,
    go: IconGo,
    java: IconJava,
    ruby: IconRuby,
    fish: IconFish,
    csharp: IconCSharp,
    zsh: IconZsh,
    rust: IconRust,
    text: IconText,
    txt: IconText,
    ansi: IconText,
    'angular-ts': IconAngular,
    'angular-html': IconAngular,
    svelte: IconSvelte,
    vimscript: IconVim,
    vim: IconVim,
    lua: IconLua,
    dart: IconDart,
    swift: IconSwift,
    php: IconPhp,
    kotlin: IconKotlin,
    elixir: IconElixir,
    haskell: IconHaskell,
    scala: IconScala,
  }
</script>

<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'

  type Props = {
    class?: string
    code?: string
    language?: string
  }
  const props = withDefaults(defineProps<Props>(), {
    code: '',
  })

  defineOptions({
    inheritAttrs: false,
  })

  const languageWithDefault = computed(() => {
    const language = props.language ?? 'text'
    if (language in languageIcons) {
      return language
    }
    return 'text'
  })

  const highlighter = await getShikiHighlighter()
  const highlightedCode = computed(() =>
    highlighter.codeToHtml(props.code, {
      lang: languageWithDefault.value,
      themes: {
        light: 'catppuccin-latte',
        dark: 'catppuccin-macchiato',
      },
    }),
  )
</script>

<template>
  <div class="group my-5">
    <div
      class="flex h-12 items-center space-x-2 rounded-t-md border border-inherit py-2.5 pr-2.5 pl-4"
    >
      <component :is="languageIcons[languageWithDefault as Langs]" />
      <span class="text-foreground-2">{{ languageWithDefault }}</span>

      <div
        class="ms-auto opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
      >
        <CopyButton
          :source="code"
          title="复制"
          class="text-foreground-2!"
        />
      </div>
    </div>

    <div
      :class="
        twMerge(
          'w-full rounded-b-lg border border-t-0 text-sm',
          '[&_code]:block [&_code]:w-full [&_code]:overflow-x-auto [&_code]:px-4 [&_code]:py-3',
          '[&_pre]:rounded-b-md not-dark:[&_pre]:bg-white/70!',
        )
      "
      v-html="highlightedCode"
    />
  </div>
</template>
