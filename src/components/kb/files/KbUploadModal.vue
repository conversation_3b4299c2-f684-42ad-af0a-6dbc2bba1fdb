<script setup lang="ts">
  import Uppy from '@uppy/core'
  import { DashboardModal } from '@uppy/vue'
  import XHRUpload from '@uppy/xhr-upload'
  import zhCN from '@uppy/locales/lib/zh_CN'

  import { injectKbDetailContext } from '@/pages/(app)/kb/[id].vue'
  import { env } from '@/../env'

  import '@uppy/core/dist/style.css'
  import '@uppy/dashboard/dist/style.css'

  const { rawKb } = injectKbDetailContext()

  const uppy = new Uppy({ locale: zhCN }).use(XHRUpload, {
    endpoint: `/kb-api/api/v1/datasets/${rawKb.value?.id}/documents/`,
    headers: {
      'X-CSRFToken': getCSRFToken(),
    },
  })

  const isOpen = defineModel<boolean>('open', { default: false })

  watchEffect(() => {
    uppy.getPlugin('XHRUpload')?.setOptions({
      endpoint: `${env.VITE_API_BASE_URL}/api/v1/datasets/${rawKb.value?.id}/documents/`,
    })
  })

  const queryClient = useQueryClient()

  uppy.on('dashboard:modal-closed', () => (isOpen.value = false))
  uppy.on('upload-success', () => {
    queryClient.invalidateQueries({ queryKey: kbQueryKey.kbFiles(rawKb.value!.id) })
  })
</script>

<template>
  <DashboardModal
    :uppy="uppy"
    :open="isOpen"
    class="reset-uppy-dashboard"
    :props="{
      proudlyDisplayPoweredByUppy: false,
      fileManagerSelectionType: 'both',
      closeAfterFinish: true,
      closeModalOnClickOutside: true,
    }"
  />
</template>
