<script setup lang="ts">
  import { injectKbDetailContext } from '@/pages/(app)/kb/[id].vue'
  import { injectKbFilesContext } from '../../KbFilesPage.vue'
  import { env } from '@/../env'

  import IconDownload from '~icons/lucide/download'

  const props = defineProps<{ rawIndex: number }>()

  const { rawFiles } = injectKbFilesContext()
  const { rawKb } = injectKbDetailContext()

  const rawFile = computed(() => rawFiles.value[props.rawIndex])

  function handleDownload() {
    const a = document.createElement('a')
    a.href = `${env.VITE_API_BASE_URL}/api/v1/datasets/${rawKb.value!.id}/documents/${rawFile.value.id}/`
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="icon-a-button"
    title="下载"
    @click="handleDownload()"
  >
    <IconDownload />
  </AButton>
</template>
