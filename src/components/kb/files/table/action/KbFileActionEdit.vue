<script setup lang="ts">
  import { injectKbFilesContext } from '../../KbFilesPage.vue'
  import type { MutationStatus } from '@tanstack/vue-query'

  import IconSetting from '~icons/lucide/settings'

  const props = defineProps<{ rawIndex: number }>()

  const { rawFiles } = injectKbFilesContext()

  const rawFile = computed(() => rawFiles.value[props.rawIndex])

  const isSettingsModalOpen = ref(false)
  const settingsFormStatus = ref<MutationStatus>('idle')
  watchEffect(() => {
    if (settingsFormStatus.value === 'success') {
      isSettingsModalOpen.value = false
    }
  })
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="icon-a-button"
    title="配置"
    @click="isSettingsModalOpen = true"
  >
    <IconSetting />

    <AModal
      v-model:open="isSettingsModalOpen"
      title="配置"
      :ok-button-props="{
        htmlType: 'submit',
        // @ts-expect-error antdv poor typing
        form: 'file-config-form',
        loading: settingsFormStatus === 'pending',
      }"
      wrap-class-name="reset-ant-modal"
      centered
    >
      <KbFileSettingsForm
        id="file-config-form"
        :raw-file="rawFile"
        @update:status="settingsFormStatus = $event"
      />
    </AModal>
  </AButton>
</template>
