<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import { injectKbDetailContext } from '@/pages/(app)/kb/[id].vue'
  import { injectKbFilesContext } from '../../KbFilesPage.vue'

  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  const props = defineProps<{ rawIndex: number }>()

  const { rawFiles } = injectKbFilesContext()
  const { rawKb } = injectKbDetailContext()

  const rawFile = computed(() => rawFiles.value[props.rawIndex])

  const queryClient = useQueryClient()
  const { mutate: deleteFile, status } = useMutation({
    mutationFn() {
      return kbFetcher(`/datasets/${rawKb.value!.id}/documents/`, {
        method: 'delete',
        body: {
          ids: [rawFile.value.id],
        },
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: kbQueryKey.kbFiles(rawKb.value!.id) })
    },
    onError(error) {
      message.error(error.message)
    },
  })
</script>

<template>
  <APopconfirm
    title="确定删除该文件吗？"
    @confirm="deleteFile()"
  >
    <AButton
      type="text"
      size="small"
      class="icon-a-button"
      title="删除"
    >
      <IconLoading
        v-if="status === 'pending'"
        class="animate-spin"
      />
      <IconTrash v-else />
    </AButton>
  </APopconfirm>
</template>
