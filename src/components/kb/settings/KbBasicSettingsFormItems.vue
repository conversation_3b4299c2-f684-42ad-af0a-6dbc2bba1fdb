<script setup lang="ts">
  import { injectKbSettingsPageContext } from './KbSettingsPage.vue'

  const { formState } = injectKbSettingsPageContext()

  const { data: embeddingModels, status: embeddingModelsStatus } = useQuery({
    queryKey: modelsQueryKey.modelList(['embedding']),
    async queryFn() {
      const { items } = await modelsFetcher<{ items: API.Models.Model[] }>('/models/', {
        query: {
          categories: ['embedding'],
        },
      })
      return items
    },
  })
</script>

<template>
  <div class="space-y-5!">
    <div class="text-foreground-2 text-lg font-bold">基本设置</div>

    <AFormItem
      required
      label="知识库名称"
    >
      <AInput v-model:value="formState.name" />
    </AFormItem>

    <AFormItem
      label="知识库描述"
      name="description"
    >
      <ATextarea v-model:value="formState.description" />
    </AFormItem>

    <AFormItem
      label="嵌入模型"
      required
      name="embeddingModel"
    >
      <ASelect
        v-model:value="formState.embeddingModel"
        :loading="embeddingModelsStatus === 'pending'"
      >
        <ASelectOption
          v-for="model in embeddingModels"
          :key="model.id"
          :value="model.name"
        >
          {{ model.name }}
        </ASelectOption>
      </ASelect>
    </AFormItem>

    <AppPermissionGuard :roles="['teacher']">
      <AFormItem
        label="关联课程"
        name="courses"
      >
        <ASelect
          v-model:value="formState.courses"
          mode="multiple"
        >
          <ASelectOption value="1">课程1</ASelectOption>
          <ASelectOption value="2">课程2</ASelectOption>
        </ASelect>
      </AFormItem>
    </AppPermissionGuard>
  </div>
</template>
