<script setup lang="ts">
  import { Tabs<PERSON><PERSON>, TabsTrigger, injectTabsRootContext } from 'reka-ui'

  const { modelValue: activeTab } = injectTabsRootContext()
  const tabsListRef = useTemplateRef('tabsListRef')
  const activeTabRef = ref<HTMLElement>()

  const { width: containerWidth } = useElementSize(() => tabsListRef.value?.$el)

  const clipLeft = ref(0)
  const clipRight = ref(0)

  watchPostEffect(() => {
    // 依赖 DOM 状态，需要在 DOM 更新后运行
    const activeElement = activeTabRef.value
    if (!activeElement) {
      return
    }
    clipLeft.value = activeElement.offsetLeft
    clipRight.value = activeElement.offsetLeft + activeElement.offsetWidth
  })

  const clipPath = computed(() => {
    if (clipRight.value === 0) {
      if (activeTab.value === 'files') {
        return 'inset(0 60px 0 0 round 50px)'
      } else {
        return 'inset(0 0 0 60px round 50px)'
      }
    }
    return `inset(0 ${Math.trunc(containerWidth.value - clipRight.value)}px 0 ${Math.trunc(clipLeft.value)}px round 50px)`
  })

  const tabs = [
    { label: '文件', value: 'files' },
    { label: '配置', value: 'settings' },
  ] as const

  function setActiveTabRef(value: string, el: HTMLElement) {
    if (value === activeTab.value) {
      activeTabRef.value = el
    }
  }
</script>

<template>
  <TabsList
    class="relative flex rounded-[50px] bg-[#3F8CFF26]"
    ref="tabsListRef"
  >
    <TabsTrigger
      v-for="tab in tabs"
      :key="tab.value"
      :value="tab.value"
      as-child
      class="tabs-button"
    >
      <button
        class="tabs-button"
        :ref="(el) => setActiveTabRef(tab.value, el as HTMLElement)"
      >
        {{ tab.label }}
      </button>
    </TabsTrigger>

    <div
      class="absolute inset-0 z-10 flex transition-[clip-path] duration-400 ease-out"
      :style="{
        'clip-path': clipPath,
      }"
    >
      <TabsTrigger
        v-for="tab in tabs"
        :key="tab.value"
        :value="tab.value"
        class="tabs-button overlay-button"
      >
        {{ tab.label }}
      </TabsTrigger>
    </div>
  </TabsList>
</template>

<style scoped>
  .tabs-button {
    padding: 4px 16px;
    cursor: pointer;
    color: var(--color-primary);

    &.overlay-button {
      color: white;
      font-weight: bold;

      &:nth-child(odd) {
        background: linear-gradient(135deg, #219fffff 0%, #0066ffff 100%);
      }
      &:nth-child(even) {
        background: linear-gradient(45deg, #0066ffff 0%, #219fffff 100%);
      }
    }
  }
</style>
