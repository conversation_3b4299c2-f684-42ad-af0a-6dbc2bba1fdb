<script lang="ts">
  import type { HTMLAttributes } from 'vue'

  export type KbCard = {
    id: string
    name: string
    description?: string
    fileNum: number
    courses?: string[]
  }

  type Props = {
    class?: HTMLAttributes['class']
    kb: KbCard
  }
</script>

<script setup lang="ts">
  import { h } from 'vue'
  import { twMerge } from 'tailwind-merge'
  import { message, Modal } from 'ant-design-vue'

  import IconEllipsis from '~icons/lucide/ellipsis'
  import IconTrash from '~icons/lucide/trash-2'
  import IconFiles from '~icons/local/kb-files'
  import IconLinks from '~icons/local/kb-links'
  import IconLoading from '~icons/lucide/loader-circle'

  const props = defineProps<Props>()

  const coursesDisplayed = computed(() => props.kb.courses?.join('; '))

  const router = useRouter()

  const queryClient = useQueryClient()
  const { mutate: deleteKb, status } = useMutation({
    mutationFn() {
      return kbFetcher('/datasets/', {
        method: 'delete',
        body: {
          ids: [props.kb.id],
        },
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: kbQueryKey.kbList() })
    },
    onError(err) {
      message.error(err.message)
    },
  })

  function handleDelete() {
    Modal.confirm({
      title: '确定删除知识库吗？',
      content: `确定删除 ${props.kb.name} 吗？`,
      onOk() {
        deleteKb()
      },
      centered: true,
      wrapClassName: 'reset-ant-modal',
    })
  }
</script>

<template>
  <div
    :class="
      twMerge(
        'flex min-w-50 cursor-pointer flex-col rounded-md bg-white p-5 pb-6 shadow-[0_2px_19px_#1D4F9905] transition-all duration-200 ease-in-out hover:translate-y-[-2px]',
        props.class,
      )
    "
    @click="router.push($route.path + '/' + props.kb.id)"
  >
    <div class="flex items-center">
      <TextEllipsis
        :lines="1"
        :text="props.kb.name"
        class="text-foreground-1 text-xl font-medium"
        :tooltip="{
          title: props.kb.name,
        }"
      />

      <ADropdown trigger="click">
        <AButton
          type="text"
          class="-mr-1.5! ml-auto min-h-0! p-1! px-1.5!"
          @click.stop
        >
          <IconEllipsis />
        </AButton>

        <template #overlay>
          <AMenu>
            <AMenuItem
              danger
              :icon="status === 'pending' ? h(IconLoading) : h(IconTrash)"
              :disabled="status === 'pending'"
              @click="handleDelete"
            >
              删除
            </AMenuItem>
          </AMenu>
        </template>
      </ADropdown>
    </div>

    <div class="text-foreground-3 pt-5 pb-11">
      <TextEllipsis
        v-if="props.kb.description"
        :text="props.kb.description"
        :lines="4"
        :tooltip="{
          title: props.kb.description,
          placement: 'top',
          overlayInnerStyle: { maxHeight: '300px', overflowY: 'auto' },
          overlayStyle: { maxWidth: '400px' },
        }"
      />
    </div>

    <div class="text-foreground-4 mt-auto flex items-center contain-inline-size">
      <div
        class="flex items-center gap-1"
        title="文件数"
      >
        <IconFiles />
        <div class="ml-1 w-9">{{ props.kb.fileNum }}</div>
      </div>

      <div
        class="ml-2 flex grow items-center gap-1 contain-inline-size"
        title="关联课程"
        v-if="coursesDisplayed"
      >
        <IconLinks class="shrink-0" />
        <TextEllipsis
          :text="coursesDisplayed"
          :lines="1"
          :tooltip="{
            title: coursesDisplayed,
            placement: 'top',
          }"
        />
      </div>
    </div>
  </div>
</template>
