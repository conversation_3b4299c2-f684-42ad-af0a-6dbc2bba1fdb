<template>
  <div class="dropdown-panel">
    <el-input
      v-model="filterText"
      clearable
      size="small"
    >
      <template #suffix>
        <el-icon><Search /></el-icon>
      </template>
    </el-input>
    <el-checkbox
      v-model="checkAll"
      @change="handleCheckAll"
      style="margin: 5px 5px 0 5px"
    >
      全选
    </el-checkbox>
    <el-tree
      ref="treeRef"
      :data="treeData"
      node-key="id"
      show-checkbox
      default-expand-all
      :props="defaultProps"
      :filter-node-method="filterNode"
      @check="handleCheck"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { ElTree } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

interface TreeNode {
  id: number | string
  label: string
  children?: TreeNode[]
}

const props = defineProps<{
  treeData: TreeNode[]
}>()

const emit = defineEmits<{
  (e: 'update:checked', checkedKeys: Array<string | number>): void
}>()

const filterText = ref('')
const checkAll = ref(false)
const treeRef = ref<InstanceType<typeof ElTree> | null>(null)

const defaultProps = {
  children: 'children',
  label: 'label'
}

// 监听搜索输入
watch(filterText, (val) => {
  treeRef.value?.filter(val)
})

// 节点过滤方法
const filterNode = (value: string, data: TreeNode): boolean => {
  if (!value) return true
  return data.label.includes(value)
}

// 全选逻辑
const handleCheckAll = (val: boolean) => {
  const allKeys: Array<string | number> = []

  const collectKeys = (nodes: TreeNode[]) => {
    for (const node of nodes) {
      allKeys.push(node.id)
      if (node.children) collectKeys(node.children)
    }
  }

  collectKeys(props.treeData)

  treeRef.value?.setCheckedKeys(val ? allKeys : [])
}

// 节点勾选更新事件
const handleCheck = () => {
  const keys = treeRef.value?.getCheckedKeys() ?? []
  emit('update:checked', keys)
}

// 向父组件暴露方法
defineExpose({
  clearCheck() {
    checkAll.value = false
    treeRef.value?.setCheckedKeys([])
  },
  getChecked() {
    return treeRef.value?.getCheckedKeys() ?? []
  }
})
</script>

<style scoped>
.dropdown-panel {
  padding: 12px;
  background: white;
  border-radius: 4px;
}
</style>
