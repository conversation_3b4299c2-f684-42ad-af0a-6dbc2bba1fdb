<script lang="ts">
  import ImgFileTypeExcel from '@/assets/img/file-type-excel.svg'
  import ImgFileTypeMarkdown from '@/assets/img/file-type-markdown.svg'
  import ImgFileTypePdf from '@/assets/img/file-type-pdf.svg'
  import ImgFileTypeTxt from '@/assets/img/file-type-txt.svg'
  import ImgFileTypeWord from '@/assets/img/file-type-word.svg'

  export type Props = {
    fileName?: string
    fileExtension?: string
  }

  const FILE_TYPE_IMAGE_MAP: Record<string, string> = {
    pdf: ImgFileTypePdf,
    md: ImgFileTypeMarkdown,
    txt: ImgFileTypeTxt,
    doc: ImgFileTypeWord,
    docx: ImgFileTypeWord,
    xls: ImgFileTypeExcel,
    xlsx: ImgFileTypeExcel,
  }
</script>

<script setup lang="ts">
  const props = defineProps<Props>()

  const ext = computed(() => {
    if (props.fileExtension) {
      return props.fileExtension
    }
    if (!props.fileName) {
      return ''
    }
    return getFilenameParts(props.fileName).ext
  })

  const image = computed(() => FILE_TYPE_IMAGE_MAP[ext.value] ?? ImgFileTypeTxt)
</script>

<template>
  <img :src="image" />
</template>
