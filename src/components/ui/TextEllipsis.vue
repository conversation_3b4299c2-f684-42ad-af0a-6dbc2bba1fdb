<script setup lang="ts">
  import type { CSSProperties } from 'vue'
  import type { TooltipProps } from 'ant-design-vue'

  type Props = {
    text: string
    lines: number

    tooltip?: TooltipProps
  }

  defineOptions({ inheritAttrs: false })

  const props = defineProps<Props>()

  const textRef = ref<HTMLElement>()
  const isOverflowing = ref(false)

  // 检查文本是否溢出
  const checkOverflow = () => {
    if (!textRef.value) return

    const element = textRef.value
    // 对于单行文本，检查 scrollWidth > clientWidth
    if (props.lines === 1) {
      isOverflowing.value = element.scrollWidth > element.clientWidth
    } else {
      // 对于多行文本，检查 scrollHeight > clientHeight
      isOverflowing.value = element.scrollHeight > element.clientHeight
    }
  }

  // 使用 ResizeObserver 监听元素大小变化
  const { stop } = useResizeObserver(textRef, () => {
    nextTick(() => {
      checkOverflow()
    })
  })

  // 监听文本内容变化
  watch(
    () => props.text,
    () => {
      nextTick(() => {
        checkOverflow()
      })
    },
  )

  // 监听行数变化
  watch(
    () => props.lines,
    () => {
      nextTick(() => {
        checkOverflow()
      })
    },
  )

  // 组件挂载后检查溢出
  onMounted(() => {
    nextTick(() => {
      checkOverflow()
    })
  })

  // 组件卸载时停止监听
  onUnmounted(() => {
    stop()
  })

  const textStyles = computed<CSSProperties>(() => {
    const styles: CSSProperties = {
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    }
    if (props.lines === 1) {
      styles.textWrap = 'nowrap'
    } else {
      styles.overflowWrap = 'break-word'
      styles.display = '-webkit-box'
      styles.WebkitBoxOrient = 'vertical'
      styles.WebkitLineClamp = props.lines
    }

    return styles
  })
</script>

<template>
  <ATooltip
    v-if="props.tooltip && isOverflowing"
    v-bind="props.tooltip"
  >
    <div
      ref="textRef"
      v-bind="$attrs"
      :style="textStyles"
    >
      {{ text }}
    </div>
  </ATooltip>

  <div
    v-else
    ref="textRef"
    v-bind="$attrs"
    :style="textStyles"
  >
    {{ text }}
  </div>
</template>
