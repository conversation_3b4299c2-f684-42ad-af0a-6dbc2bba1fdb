<template>
    <div class="clearfix" style="display: flex;align-items: center;">
        <div>
            <a-upload v-model:file-list="fileList" list-type="picture-card" 
                @preview="handlePreview" @remove="handleRemove" :before-upload="beforeUpload">
                <div>参考资料</div>
                                <img src="@/assets/image/quetion/ziliaoupload.png" style="width: 56px;height: 52px;margin: 11px auto 0;" />

            </a-upload>
        </div>
        
    </div>
</template>
<script lang="ts" setup>
import { PlusOutlined } from '@ant-design/icons-vue';
import { ref } from 'vue';
import type { UploadProps } from 'ant-design-vue';

function getBase64(file: File) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// const fileList = ref<UploadProps['fileList']>([]);
const fileList = ref([]);
const handleRemove: UploadProps['onRemove'] = file => {
    fileList.value = [];
    emits('deleteFile', null)
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
    //   console.log(file,'上传文件获取文件');
    emits('beforeUploadgetfile', file)
    return false;
};
const emits = defineEmits(['beforeUploadgetfile', 'deleteFile'])


const handleCancel = () => {
    previewVisible.value = false;
    previewTitle.value = '';
};
// const handlePreview = async (file: UploadProps['fileList'][number]) => {
const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
        file.preview = (await getBase64(file.originFileObj)) as string;
    }
    previewImage.value = file.url || file.preview;
    previewVisible.value = true;
    previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
};
</script>
<style scoped lang="scss">
:deep(.ant-upload-select-picture-card) {
    width: 336px !important;
    height: 180px !important;
    background-color: #fff !important;
}

:deep(.ant-upload-list-item-container) {
    width: 336px !important;
    height: 180px !important;
    background-color: #fff !important;
}


/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;

    /* background-color: #fff; */
    /* width: 336px;
    height: 180px; */
}
</style>
