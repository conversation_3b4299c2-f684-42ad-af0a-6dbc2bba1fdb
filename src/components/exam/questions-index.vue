<template>
    <div class="question pt-[20px] px-[20px] pb-[170px]">
        <div class="top" ref="topButton">
            <div class="flex justify-between items-center leading-[12px]">
                <div class="font-bold text-[20px]">作业名称</div>
                <a-button type="text" style="width: 82px;height: 32px;color: #fff;font-weight: 600;font-size: 14px;border: none;
              background: linear-gradient(135.84deg, #219FFF 0%, #0066FF 100%);" @click="">提交</a-button>
            </div>
        </div>
        <div
            class="flex justify-between items-center mt-[13px] text-[14px] pb-[23px] border-b-[1px] border-solid border-[#E8E8E8]">
            <div class="flex">
                <div class="text-[#666666]">学生:<span class="text-[#333333]">张三</span></div>
                <div class="text-[#666666] ml-[34px]">学号:<span class="text-[#333333]">97</span></div>
            </div>
            <div class="font-bold">
                题量5道，总分100分
            </div>
        </div>

        <div v-for="(item, index) in examList" :key="index" style="border-bottom: 1px dashed #E8E8E8;"
            class="py-[20px]">
            <div class="flex mb-[10px] ">
                <div class="flex shrink-0">
                    {{ index + 1 }}、
                    <div class="text-[#3F8CFF] font-medium mr-[10px]">
                        [{{ item.choiseTypName }}]
                    </div>
                </div>
                <div>
                    {{ item.qsTitle }}
                </div>
            </div>
            <Questions :item="item" @answerChange="answerChange"></Questions>
        </div>

        <div class="fixed bottom-[55px] right-[149px] z-10" v-show="showFloatButton">
            <a-button type="text" style="width: 82px;height: 32px;color: #fff;font-weight: 600;font-size: 14px;border: none;
              background: linear-gradient(135.84deg, #219FFF 0%, #0066FF 100%);" @click="">提交</a-button>
        </div>
    </div>


</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import Questions from './questions.vue'
const questionStore = useQuestionStore() // 获取当前题号

//题目列表
defineProps(['examList'])
const emit = defineEmits(['changeOptions', 'submitExam']);
// 单项选择
function answerChange(item: Object) {
    let options = {
        'answerData': item,
        'examIndex': questionStore.titleNumber - 1
    }
    emit('changeOptions', options);
}

// 提交答案
function submitAnswer() {
    console.log('提交答案')
    emit('submitExam')
}


//判断提交按钮是否显示

const topButton = ref(null);
const showFloatButton = ref(false);
const initObserver = () => {
    const observer = new IntersectionObserver(
        (entries) => {
            entries.forEach(entry => {
                showFloatButton.value = !entry.isIntersecting;
            });
        },
        {
            root: null, // 视口为根
            threshold: 0.1, // 10% 可见时触发
        }
    );

    if (topButton.value) {
        observer.observe(topButton.value);
    }

    // 组件卸载时停止监听
    onUnmounted(() => {
        observer.disconnect();
    });
};

onMounted(() => {
    initObserver();
});
</script>
<style lang="scss" scoped>
.question {
    width: 1030px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    height: calc(100vh - 100px);
    overflow: auto;
}
</style>
