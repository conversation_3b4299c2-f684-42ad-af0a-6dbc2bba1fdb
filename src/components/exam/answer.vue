<template>
    <div class="answer p-[20px]">
        <div>
            <div class="font-bold text-[16x] leading-[16x] flex items-center">
                <div class="w-[4px] h-[16px] bg-[#36AAFD] mr-[8px] rounded-[2px]"></div>
                答题卡
            </div>
            <div class="flex mt-[20px]  gap-2 flex-wrap max-h-[270px] hide-scrollbar overflow-y-auto">
                <div v-for="(item, index) in 20" :key="index" 
                    class="common font-bold text-[14x] text-white "
                    :class="getItemClass(index)" @click="chooseItem(index + 1)">
                    <div>{{ index + 1 }}</div>
                </div>
            </div>
        </div>

        <div class="flex justify-between items-center h-[28px] w-auto" style="">
            <div class="flex items-center text-[12x] text-[#666666]">
                <div class="already mr-[4px]"></div>
                <div class="mr-[14px]">已做</div>
                <div class="nodo mr-[4px]"></div>未做
            </div>
            <div class="flex items-center">
                <div>剩余时间</div>
                <div class="font-bold text-[#333333] text-[20px]">
                    <!-- 12:22:32 -->
                    <Examtime />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>

import Examtime from './examtime.vue';
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'

const props = defineProps(['examList'])

const questionStore = useQuestionStore()  //获取pinia里面的题号数据

const chooseItem = (index: number) => {
    questionStore.updateTitleNumber(index) //修改题号
}

// 动态 class 的逻辑
const getItemClass = (index: number) => {
    const item = props.examList[index];
    //   if(questionStore.titleNumber === index + 1){
    //     return 'active1';
    //   }else if(item.userAnswer.length > 0 && item.userAnswer[0] !== ''){
    //       return 'active';
    //   }else{
    //       return 'active';
    //   }
    if (index == 2) {
        return 'active1';
    } else if (index != 2) {
        return 'active';
    } else {
        return 'active';
    }
};

</script>
<style lang="scss" scoped>
.answer {
    width: 350px;
    min-width: 300px;
    height: 396px;
    opacity: 1;
    border-radius: 2.61px;
    background: #FFFFFF;
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    // flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
}

.hide-scrollbar {
    &::-webkit-scrollbar {
        display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
}


.common {
    border-radius: 2px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    cursor: pointer;
    text-align: center;
}

.active1 {
    background: rgba(245, 34, 45, 0.5);
}

.active {
    background: rgba(63, 140, 255, 1);
}

// .answer-remark{
//     margin-top: 4vh;
//     display: flex;
//     justify-content: space-around;
// }
.already {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    background: rgba(245, 34, 45, 0.5);
}

.nodo {
    width: 12px;
    height: 12px;
    background: rgba(63, 140, 255, 1);
    border-radius: 2px;
    margin-right: 4px;
}
</style>
