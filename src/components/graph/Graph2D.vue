<template>
    <div class="layout">
        <div ref="containerRef" class="graph-container"></div>
        <!-- <FunctionalArea style="width: 300px;" :selectedNode="isEditMode ? updadeData : null" :isEditing="isEditMode" :updateLine="updateLine" /> -->
        <!-- 右键菜单 -->
        <div ref="contextMenuRef" class="context-menu" v-show="isContextMenuVisible"
            :style="{ left: menuLeft + 'px', top: menuTop + 'px' }">
            <ul>
                <template v-if="rightClickedObject === 'node'">
                    <li @click="handleMenuClick('addNode')">
                        <img src="@/assets/image/delete.png" alt="新增"
                            style="width: 12px; height: 12px; margin-right: 4px;" />新增
                    </li>
                    <li @click="handleMenuClick('deleteNode')">
                        <img src="@/assets/image/delete.png" alt="删除"
                            style="width: 12px; height: 12px; margin-right: 4px;" />删除
                    </li>
                    <li @click="handleMenuClick('addLine')">
                        <img src="@/assets/image/arrow.png" alt="添加边"
                            style="width: 12px; height: 12px; margin-right: 4px;" />添加边
                    </li>
                    <li @click="handleMenuClick('updateNode')">
                        <img src="@/assets/image/update.png" alt="编辑节点"
                            style="width: 12px; height: 12px; margin-right: 4px;" />编辑节点
                    </li>
                </template>
                <template v-else-if="rightClickedObject === 'line'">
                    <li @click="handleMenuClick('updateLine')">
                        <img src="@/assets/image/update.png" alt="编辑"
                            style="width: 12px; height: 12px; margin-right: 4px;" />编辑
                    </li>
                    <li @click="handleMenuClick('deleteLine')">
                        <img src="@/assets/image/delete.png" alt="删除"
                            style="width: 12px; height: 12px; margin-right: 4px;" />删除
                    </li>
                </template>
            </ul>
        </div>

        <!-- 左键详情菜单 -->
        <div ref="detailsMenuRef" class="details-menu" v-show="isDetailsMenuVisible"
            :style="{ left: menuLeft + 'px', top: menuTop + 'px' }">
            <ul>
                <template v-if="leftClickedObject === 'node'">
                    <li style="font-weight: 700;"> 节点详情 </li>
                    <li> 名称: {{ currentClickedNode.properties.name || '无名称' }}</li>
                    <li> ID: {{ currentClickedNode.id || '无ID' }} </li>
                    <li> 标签: {{ currentClickedNode.label || '无标签' }} </li>
                    <li> 组别: {{ currentClickedNode.properties.content || '无内容' }} </li>
                </template>
                <template v-else-if="leftClickedObject === 'line'">
                    <li style="font-weight: 700;"> 连线详情 </li>
                    <li> ID: {{ currentClickedLine.id.match(/'relationId'\s*:\s*'([^']+)'/)[1] }} </li>
                    <li> 标签: {{ currentClickedLine.label }} </li>
                    <li> 起始节点: {{ currentClickedLine.source.properties.name }} </li>
                    <li> 目标节点: {{ currentClickedLine.target.properties.name }} </li>
                </template>
            </ul>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as d3 from "d3";
import { ref, onMounted, onUnmounted, watch } from "vue";
import { useElementSize } from '@vueuse/core';
import FunctionalArea from './FunctionalArea.vue'
import { deleteGraphNode, addGraphEdge, deleteGraphEdge } from "@/services/api/graph";
import { message } from 'ant-design-vue';

const props = defineProps({
    nodes: {
        type: Array as () => any[],
        default: () => []
    },
    links: {
        type: Array as () => any[],
        default: () => []
    },
    getGraphList: {
        type: Function,
        required: true
    },
});

const emit = defineEmits(['deleteNode','addNode','updateNode','updateEdge']); // 定义删除节点事件


const containerRef = ref<HTMLElement | null>(null);
const contextMenuRef = ref<HTMLElement | null>(null);
const detailsMenuRef = ref<HTMLElement | null>(null);

const { width, height } = useElementSize(containerRef);
let simulation: d3.Simulation<d3.SimulationNodeDatum, undefined>;
const isContextMenuVisible = ref(false);
const isDetailsMenuVisible = ref(false);
const menuLeft = ref(0); //存储菜单位置
const menuTop = ref(0);//存储菜单位置
let currentClickedNode: any;
let currentClickedLine: any;
const rightClickedObject = ref<string | null>(null); //右键点击的对象标记
const leftClickedObject = ref<string | null>(null); //左键点击的对象标记
const isEditMode = ref(false); //表示编辑的状态
let updadeData: any//更新节点数据
let updateLine: any; //更新连线数据
const isAddingLine = ref(false) //添加连线状态
let tempLine: any; //临时虚线
let startNode: any; //起始节点
const onTargetNodeSelected = ref<((targetNode: any) => void) | null>(null);
let detailsElement: HTMLElement | null = null; //节点详情
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'


// 渲染2D图形的主函数
const render2DGraph = async (container: HTMLElement) => {
    if (!container) return;

    console.log('2D Graph Data:', props.nodes, props.links);

    // 清理现有的 SVG
    d3.select(container).selectAll("*").remove();

    // 使用 API 获取的数据
    const graphData = { nodes: props.nodes, links: props.links };
    console.log('2D Graph Data:', graphData);
    if (!graphData || !graphData.nodes.length) {
        return
    };

    // 使用固定的尺寸
    const containerWidth = 800;
    const containerHeight = 600;

    const svg = d3
        .select(container)
        .append("svg")
        .attr("width", "100%")
        .attr("height", "100%")
        .attr("viewBox", `0 0 ${containerWidth} ${containerHeight}`)
        .attr("preserveAspectRatio", "xMidYMid meet");

    // 添加缩放和平移功能
    const zoom = d3
        .zoom()
        .scaleExtent([0.1, 4])
        .on("zoom", (event) => {
            g.attr("transform", event.transform);
        });

    svg.call(zoom as any);

    // 创建一个包含所有元素的组
    const g = svg.append("g");

    // 创建力导向图
    simulation = d3
        .forceSimulation(graphData.nodes as any)
        .force(
            "link",
            d3
                .forceLink(graphData.links)
                .id((d: any) => d.id.toString())
                .distance(150)
        )
        .force("charge", d3.forceManyBody().strength(-100))  //设置节点间的排斥力
        .force("center", d3.forceCenter(containerWidth/1.5, containerHeight / 1.5)) //设置所有节点向中心点
        .force("collision", d3.forceCollide().radius(40)); //设置节点间的碰撞检测

    // 绘制连接线
    const link = g
        .append("g")
        .selectAll("line")
        .data(graphData.links)
        .enter()
        .append("line")
        .attr("stroke", "#e0cac1")
        .attr("stroke-opacity", 0.6)
        .attr("stroke-width", 3)
        //添加线点击事件使线能够被选中
        .on("click", function (event: MouseEvent, d) {
            //重置所有线的样式
            link.attr("stroke", "#e0cac1")
                .attr("stroke-width", 3)
            //选中当前点击的线并更换样式
            const clickedLine = d3.select(this);
            clickedLine
                .attr("stroke", "blue")
                .attr("stroke-width", 4);
            
                showDetails(event, d,'line');

            //console.log('被点击线的信息', d)
        })

    // 绘制中间箭头
    const arrow = g
        .append("g")
        .selectAll(".arrow")
        .data(graphData.links)
        .enter()
        .append("path")
        .attr("class", "arrow")
        .attr("fill", "#e0cac1")
        .attr("stroke", "none");

    // 创建节点组
    const node = g
        .append("g")
        .selectAll(".node")
        .data(graphData.nodes)
        .enter()
        .append("g")
        .attr("class", "node")
        .call(drag(simulation) as any);

    // 创建颜色生成函数
    const getNodeColor = d3.scaleOrdinal(d3.schemeCategory10);

    // 绘制节点圆
    node
        .append("circle")
        .attr("r", 30)
        .attr("fill", (d: any) => getNodeColor(d.id)) // 使用颜色生成函数设置节点颜色
        .attr("stroke", "#fff")
        .attr("stroke-width", 2);

    // 添加节点标签
    node
        .append("text")
        .attr("dy", ".35em")
        .attr("text-anchor", "middle")
        .text((d: any) => d.properties.name)
        .attr("fill", "white")
        .style("font-size", "12px")
        .style("font-weight", "bold");

    // 添加点击高亮效果
    node.on("click", function (event: MouseEvent, d) {
        // 重置所有节点和连接线的样式
        node.select("circle")
            .attr("r", 30)
            .attr("stroke", "#fff")
            .style("opacity", 0.3); // 将未选中的节点变灰
        node.select("text")
            .style("font-size", "12px")
            .style("opacity", 0.3); // 将未选中的文本变灰
        link
            .attr("stroke", "#999")
            .attr("stroke-width", 1.5)
            .style("opacity", 0.3); // 将未选中的连接线变灰

        // 高亮被点击的节点及其相关连接
        const clickedNode = d3.select(this);
        clickedNode.select("circle")
            .attr("r", 35)
            .attr("stroke", "#ff0")
            .style("opacity", 1); // 恢复选中节点的不透明度
        clickedNode.select("text")
            .style("font-size", "14px")
            .style("opacity", 1); // 恢复选中节点文本的不透明度

        // 高亮与被点击节点相连的所有连接线和节点
        link.filter((l: any) => l.source.id.toString() === d.id.toString() || l.target.id.toString() === d.id.toString())
            .attr("stroke", "#ff0")
            .attr("stroke-width", 3)
            .style("opacity", 1) // 恢复相关连接线的不透明度
            .each(function (l: any) {
                // 高亮相连的节点
                const connectedNode = l.source.id.toString() === d.id.toString() ? l.target : l.source;
                node.filter((n: any) => n.id.toString() === connectedNode.id.toString())
                    .select("circle")
                    .style("opacity", 1)
                    .attr("stroke", "#ff0");
                node.filter((n: any) => n.id.toString() === connectedNode.id.toString())
                    .select("text")
                    .style("opacity", 1);
            });

        // 停止模拟
        simulation.stop();

        //添加连线逻辑
        if (isAddingLine.value && onTargetNodeSelected.value) {
            onTargetNodeSelected.value(d);
            /* console.log('起始节点数据:', startNode);
            console.log('目标节点数据:', d);
            isAddingLine.value = false;
            if (tempLine) {
                tempLine.remove();
            } */
        }
        showDetails(event, d,'node');
    });

    // 添加鼠标虚线移动事件
    svg.on("mousemove", function (event) {
        if (isAddingLine.value) {
            const [mx, my] = d3.pointer(event);
            const svg = d3.select(containerRef.value).select("svg");//选中节点的svg
            const transform = d3.zoomTransform(svg.node() as SVGSVGElement); //获取选中节点缩放变换的位置
            //应用缩放变换到起始位置
            const scaledX = transform.applyX(startNode.x);
            const scaledY = transform.applyY(startNode.y);
            tempLine
                .attr("x1", scaledX)
                .attr("y1", scaledY)
                .attr("x2", mx)
                .attr("y2", my);
        }
    });

    //节点和线右键菜单
    const showContextMenu = (event: MouseEvent, element: SVGGraphicsElement, type: string, data: any) => {
        isDetailsMenuVisible.value = false; // 隐藏详情菜单
        event.preventDefault();
        if (type === 'node') {
            currentClickedNode = data;
        } else if (type === 'line') {
            currentClickedLine = data;
        }
        rightClickedObject.value = type;
        isContextMenuVisible.value = true;

        const rect = containerRef.value!.getBoundingClientRect()
        const menuWidth = 130
        const menuHeight = 160

        // 将client坐标转为容器内部坐标
        let x = event.clientX - rect.left 
        let y = event.clientY - rect.top

        if (x + menuWidth > rect.width) {
            x = rect.width - menuWidth
        }
        if (y + menuHeight > rect.height) {
            y = rect.height - menuHeight
        }

        // 设置菜单的位置
        menuLeft.value = x + 10;
        menuTop.value = y + 10;
       
    };

    // 添加节点右键点击事件
    node.on("contextmenu", function (event: MouseEvent, d) {
        showContextMenu(event, this as SVGGraphicsElement, 'node', d);
    });
    // 添加线右键点击事件
    link.on("contextmenu", function (event: MouseEvent, d) {
        showContextMenu(event, this as SVGGraphicsElement, 'line', d);
    });

    // 详情菜单
    const showDetails = (event: MouseEvent, data: any, type: any ) => {
        isContextMenuVisible.value = false; // 隐藏右键菜单
        if (type === 'node') {
            currentClickedNode = data;
        } else if (type === 'line') {
            currentClickedLine = data;
        }
        leftClickedObject.value = type;
        isDetailsMenuVisible.value = true;

        const rect = containerRef.value!.getBoundingClientRect()
        const menuWidth = 130
        const menuHeight = 160

        // 将client坐标转为容器内部坐标
        let x = event.clientX - rect.left 
        let y = event.clientY - rect.top

        if (x + menuWidth > rect.width) {
            x = rect.width - menuWidth
        }
        if (y + menuHeight > rect.height) {
            y = rect.height - menuHeight
        }

        // 设置菜单的位置
        menuLeft.value = x + 10;
        menuTop.value = y + 10;
    
    };

    // 添加点击背景重置效果
    svg.on("click", function (event) {
        if (event.target === this) {
            // 重置所有节点和连接线的样式
            node.select("circle")
                .attr("r", 30)
                .attr("stroke", "#fff")
                .style("opacity", 1);
            node.select("text")
                .style("font-size", "12px")
                .style("opacity", 1);
            link
                .attr("stroke", "#e0cac1")
                .attr("stroke-width", 3)
                .style("opacity", 1);

            // 重新启动模拟
            simulation.restart();
            isContextMenuVisible.value = false; // 隐藏右键菜单
            isDetailsMenuVisible.value = false; // 隐藏详情菜单

            isAddingLine.value = false;
            if (tempLine) {
                tempLine.remove();
            }
        }
    });

    // 为线添加 label 文本
    const linkLabels = g
      .append("g")
      .selectAll(".link-label")
      .data(graphData.links)
      .enter()
      .append("text")
      .attr("class", "link-label")
      .attr("fill", "#fff")
      .style("font-size", "10px")
      .style("text-anchor", "middle")
      .style("dominant-baseline", "central");

    // 更新力导向图
    simulation.on("tick", () => {
        link
            .attr("x1", (d: any) => d.source.x)
            .attr("y1", (d: any) => d.source.y)
            .attr("x2", (d: any) => d.target.x)
            .attr("y2", (d: any) => d.target.y);

        node.attr("transform", (d: any) => `translate(${d.x},${d.y})`);

        // 更新箭头位置
        arrow.attr("d", (d: any) => {
            const x1 = d.source.x;
            const y1 = d.source.y;
            const x2 = d.target.x;
            const y2 = d.target.y;
            const offsetRatio = 0.8;
            const midX = x1 + (x2 - x1) * offsetRatio;
            const midY = y1 + (y2 - y1) * offsetRatio;
            const dx = x2 - x1;
            const dy = y2 - y1;
            const angle = Math.atan2(dy, dx);
            const arrowSize = 8;
            const arrowPath = `M ${midX} ${midY} 
                           L ${midX - arrowSize * Math.cos(angle - Math.PI / 6)} ${midY - arrowSize * Math.sin(angle - Math.PI / 6)} 
                           L ${midX - arrowSize * Math.cos(angle + Math.PI / 6)} ${midY - arrowSize * Math.sin(angle + Math.PI / 6)} 
                           Z`;
            return arrowPath;
        });

        // 更新线的 label 位置
        linkLabels
          .attr("x", (d: any) => (d.source.x + d.target.x) / 2)
          .attr("y", (d: any) => (d.source.y + d.target.y) / 2)
          .text((d: any) => d.label);
    });


};


// 实现拖拽功能
const drag = (simulation: d3.Simulation<d3.SimulationNodeDatum, undefined>) => {
    function dragstarted(event: any) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        event.subject.fx = event.subject.x;
        event.subject.fy = event.subject.y;
    }

    function dragged(event: any) {
        event.subject.fx = event.x;
        event.subject.fy = event.y;
    }

    function dragended(event: any) {
        if (!event.active) simulation.alphaTarget(0);
        event.subject.fx = null;
        event.subject.fy = null;
    }

    return d3
        .drag()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended);
};

// 监听容器大小变化
watch([width, height], async () => {
    if (containerRef.value) {
        const svg = d3.select(containerRef.value).select("svg");
        if (!svg.empty()) {
            const containerWidth = containerRef.value.clientWidth;
            const containerHeight = containerRef.value.clientHeight;
            const width = Math.max(containerWidth, 800);
            const height = Math.max(containerHeight, 600);

            svg
                .attr("width", "100%")
                .attr("height", "100%")
                .attr("viewBox", [0, 0, width, height])
                .attr("preserveAspectRatio", "xMidYMid meet");
        }
    }
});

// 监听 nodes 和 links 的变化
watch([() => props.nodes, () => props.links], async () => {
    console.log('nodes or links changed', props.nodes, props.links);
    if (containerRef.value) {
        await render2DGraph(containerRef.value);
    }
});

//右键菜单编辑节点选项
const updateMenuItemClick = (option: string) => {
    console.log(`点击了选项: ${option}`);
    console.log('被点击节点的信息:', currentClickedNode);
    updadeData = currentClickedNode;
    isEditMode.value = true;
    isContextMenuVisible.value = false;
};

// 右键菜单添加连线选项
const addLineClick = (option: string) => {
    console.log(`点击了选项: ${option}`);
    isAddingLine.value = true;
    startNode = currentClickedNode;
    isContextMenuVisible.value = false;

    const svg = d3.select(containerRef.value).select("svg");
    const foundNode = simulation.find(startNode.x, startNode.y);
    const { x, y } = foundNode || startNode; // 如果没找到，使用 startNode 本身的 x 和 y
    // const transform  = d3.zoomTransform(svg.node() as SVGSVGElement); //获取当前选中节点缩放变换的位置
    // // 应用缩放变换到起始位置
    // const scaledX = transform.applyX(x);
    // const scaledY = transform.applyY(y);
    tempLine = svg.append("line")
        .attr("x1", x)
        .attr("y1", y)
        .attr("x2", x)
        .attr("y2", y)
        .attr("stroke", "red")
        .attr("stroke-dasharray", "5,5")
        .style("pointer-events", "none"); // 让鼠标事件穿透虚线

    onTargetNodeSelected.value = async (targetNode) => {
        console.log('起始节点数据:', startNode);
        console.log('目标节点数据:', targetNode);
        const form = {
            label: "暂无",
            from_vertex_id: startNode.id,
            to_vertex_id: targetNode.id,
            properties: {
                type: "武将",
                userID: "99"
            }
        };
        console.log("!!!!!!!!!!!!!!!!!!!!!!!!!!!",form);
        try {            
            const response = addGraphEdge(form);
            console.log('添加边成功', response);
            message.success('添加连线成功！');
            await props.getGraphList();
        } catch (error) {
            console.log('添加边失败', error)
            message.error('添加连线失败！')
        }
        isAddingLine.value = false;
        if (tempLine) {
            tempLine.remove();
        }
    };
};

// 统一的菜单点击处理函数
const handleMenuClick = async (option: string) => {
    //console.log(`点击了菜单选项: ${option}`);
    try {
        //添加边
        if (option === 'addLine') {
            addLineClick('option2');
        }
        //添加节点
        else if (option === 'addNode') {            
            emit('addNode',{ mode:"添加节点" });
        }
        //删除节点
        else if (option === 'deleteNode') {
            const confirmed = await showDeleteConfirm('确定要删除吗？删除后无法恢复！');
            if (confirmed) {
                const form = {
                    vertex_id: currentClickedNode.id
                };
                const response = await deleteGraphNode(form);
                console.log('删除节点成功', response);
                message.success('删除节点成功！');
                const newNodes = props.nodes.filter(node => node.id !== currentClickedNode.id);
                const newLinks = props.links.filter(link => link.source.id !== currentClickedNode.id && link.target.id !== currentClickedNode.id);
                emit('deleteNode', { nodes: newNodes, links: newLinks });
            }
        } 
        //编辑节点
        else if (option === 'updateNode') {
            updateMenuItemClick('option2');
            emit('updateNode',{ 
                mode: "编辑节点", 
                id: currentClickedNode.id,
                name: currentClickedNode.properties.name,
                content: currentClickedNode.properties.content
            });
        } 
        //删除边
        else if (option === 'deleteLine') {
            const confirmed = await showDeleteConfirm('确定要删除吗？删除后无法恢复！');
            if (confirmed) {
                const edgeId = currentClickedLine.id.match(/'relationId'\s*:\s*'([^']+)'/)[1]
                console.log("edgeId",edgeId);
                const form = {
                    edge_id: edgeId
                };
                const response = await deleteGraphEdge(form);
                console.log('删除连线成功', response);
                message.success('删除连线成功！');
                const newLinks = props.links.filter(link => link.id !== currentClickedLine.id.match(/'relationId'\s*:\s*'([^']+)'/)[1]);
                emit('deleteNode', { nodes: props.nodes, links: newLinks });
            }
        }
        //编辑边
        else if (option === 'updateLine'){
            console.log('被点击的连线数据:', currentClickedLine);
            updateLine = currentClickedLine;
            console.log('被点击的连线数据666:', updateLine);
            emit('updateEdge',{ 
                mode:'编辑边', 
                id: currentClickedLine.id.match(/'relationId'\s*:\s*'([^']+)'/)[1],
                label: currentClickedLine.label
            });
        }

    } catch (error) {
        message.error(`操作失败: ${error}`);
        console.log('操作失败', error);
    }
    isContextMenuVisible.value = false;
};

onMounted(async () => {
    if (containerRef.value) {
        await render2DGraph(containerRef.value);
    }
});

onUnmounted(() => {
    if (simulation) {
        simulation.stop();
    }
    if (containerRef.value) {
        d3.select(containerRef.value).selectAll("*").remove();
    }
    const detailsElement = document.getElementById('details-element');
    if (detailsElement) {
        detailsElement.parentNode?.removeChild(detailsElement);
    }
});
</script>

<style scoped>
.layout {
    display: flex;
    width: 100%;
    height: 100%;
}

.graph-container {
    width: 100%;
    height: 100%;
    /* min-height: 600px;
    min-width: 800px; */
    background: #9dadc1;
    position: relative;
    overflow: hidden;
    flex: 1;
    /* 让容器填充剩余空间 */
}

.context-menu {
    position: absolute;
    width: 115px;
    border-radius: 2.95px;
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.1);
    background-color: white;
    padding: 12px;
    font-size: 14px;
    z-index: 1;
    font-weight: 500;
    line-height: 14px;
    color: rgba(102, 102, 102, 1);
}

.context-menu ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.context-menu li {
    display: flex;
    justify-content: left;
    align-items: center;
    cursor: pointer;
    padding: 4px 6px;
    gap: 5px
}

.context-menu li:hover {
    background-color: #f0f0f0;
}

.details-menu {
    position: absolute;
    border-radius: 2.95px;
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.1);
    background-color: white;
    padding: 12px;
    font-size: 14px;
    z-index: 1;
    font-weight: 500;
    line-height: 14px;
    color: rgba(102, 102, 102, 1);
    overflow: auto;
}

.details-menu ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.details-menu li {
    display: flex;
    justify-content: left;
    align-items: center;
    cursor: pointer;
    padding: 4px 6px;
    gap: 5px
}

.details-menu li:hover {
    background-color: #f0f0f0;
}

</style>