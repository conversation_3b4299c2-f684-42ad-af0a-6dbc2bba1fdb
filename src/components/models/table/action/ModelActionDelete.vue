<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import { injectModelsContext } from '@/pages/(app)/models.vue'

  import IconTrash from '~icons/lucide/trash-2'
  import IconLoading from '~icons/lucide/loader-circle'

  type Props = {
    rawIndex: number
  }

  const props = defineProps<Props>()

  const { rawModels } = injectModelsContext()

  const rawModel = computed(() => rawModels.value[props.rawIndex])

  const queryClient = useQueryClient()
  const { mutate: deleteModel, status } = useMutation({
    async mutationFn() {
      return modelsFetcher(`/models/${rawModel.value.id}/`, {
        method: 'delete',
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: modelsQueryKey.modelList() })
    },
    onError(error) {
      message.error(error.message)
    },
  })
</script>

<template>
  <APopconfirm
    title="确定删除该模型吗？"
    @confirm="deleteModel()"
  >
    <AButton
      type="text"
      size="small"
      class="icon-a-button"
      title="删除"
    >
      <IconLoading
        v-if="status === 'pending'"
        class="animate-spin"
      />
      <IconTrash v-else />
    </AButton>
  </APopconfirm>
</template>
