<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import { injectModelsContext } from '@/pages/(app)/models.vue'

  import IconPlay from '~icons/lucide/circle-play'
  import IconLoading from '~icons/lucide/loader-circle'

  type Props = {
    rawIndex: number
  }

  const props = defineProps<Props>()

  const { rawModels } = injectModelsContext()

  const rawModel = computed(() => rawModels.value[props.rawIndex])

  const queryClient = useQueryClient()
  const { mutate: runModel, status } = useMutation({
    async mutationFn() {
      return modelsFetcher(`/models/${rawModel.value.id}/`, {
        method: 'put',
        body: <API.Models.UpdateModel>{
          ...rawModel.value,
          replicas: 1,
        },
      })
    },
    onSuccess() {
      return queryClient.invalidateQueries({ queryKey: modelsQueryKey.modelList() })
    },
    onError(error) {
      message.error(error.message)
    },
  })
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="icon-a-button"
    title="运行"
    @click="runModel()"
  >
    <IconLoading
      v-if="status === 'pending'"
      class="animate-spin"
    />
    <IconPlay v-else />
  </AButton>
</template>
