<script setup lang="ts">
  import { message } from 'ant-design-vue'

  import { injectModelsContext } from '@/pages/(app)/models.vue'

  import IconStop from '~icons/lucide/circle-stop'
  import IconLoading from '~icons/lucide/loader-circle'

  type Props = {
    rawIndex: number
  }

  const props = defineProps<Props>()

  const { rawModels } = injectModelsContext()

  const rawModel = computed(() => rawModels.value[props.rawIndex])

  const queryClient = useQueryClient()
  const { mutate: stopModel, status } = useMutation({
    async mutationFn() {
      return modelsFetcher(`/models/${rawModel.value.id}/`, {
        method: 'put',
        body: <API.Models.UpdateModel>{
          ...rawModel.value,
          replicas: 0,
        },
      })
    },
    async onSuccess() {
      await new Promise((resolve) => setTimeout(resolve, 200)) // 立刻刷新的话部分数据有问题
      return queryClient.invalidateQueries({ queryKey: modelsQueryKey.modelList() })
    },
    onError(error) {
      message.error(error.message)
    },
  })
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="icon-a-button"
    title="停止"
    @click="stopModel()"
  >
    <IconLoading
      v-if="status === 'pending'"
      class="animate-spin"
    />
    <IconStop v-else />
  </AButton>
</template>
