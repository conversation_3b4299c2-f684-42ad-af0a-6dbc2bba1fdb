<template>
  <div class="table-container">
  <el-table :data="students" :cell-style="{ textAlign:'center' }" :header-cell-style="{ 'color': '#333333', 'text-align': 'center' }" style="max-width: 100%;" max-height="65vh" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="50" />
    <el-table-column prop="name" label="姓名" width="80" show-overflow-tooltip />
    <el-table-column prop="studentId" label="学号" show-overflow-tooltip />
    <el-table-column prop="department" label="院系" show-overflow-tooltip />
    <el-table-column prop="major" label="专业" show-overflow-tooltip />
    <el-table-column label="操作" width="80">
      <template #default="scope">
        <el-popconfirm
          confirm-button-text="确认"
          cancel-button-text="否"
          :icon="InfoFilled"
          icon-color="#626AEF"
          title="确定删除此条学生记录吗？"
          @confirm="confirmDelete(scope.row.id)"
          @cancel="cancelEvent"
        >
          <template #reference>
            <el-tooltip effect="dark" content="删除" placement="top">
              <el-button type="text">
                <img src="@/assets/icon/delete-box.svg" alt="自定义图标" style="width: 1em; height: 1em; fill: currentColor;" />
              </el-button>
            </el-tooltip>
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
    <template #empty>
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 32px 0;">
          <img src="@/assets/icon/noData.png" alt="暂无数据" style="width: 120px; height: 120px; object-fit: contain;" />
          <div style="color: #909399; margin-top: 12px;">暂无学生数据</div>
        </div>
      </template>
  </el-table>
  </div>
  <el-pagination
    v-if="students.length > 0"
    class="custom-pagination"
    @current-change="$emit('page-change', $event)"
    :current-page="currentPage"
    :page-size="10"
    :total="total"
    size="small"
    layout="prev, pager, next"
  />
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from 'vue'
import { Delete, InfoFilled } from '@element-plus/icons-vue'

export interface StudentType {
  id: number
  name: string
  studentId: string
  department: string
  major: string
}

const props = defineProps<{ students: StudentType[], currentPage: number, total: number }>()
const emits = defineEmits(['delete-student', 'page-change', 'selection-change'])

const handleSelectionChange = (selection: StudentType[]) => {
  emits('selection-change', selection)
}

const confirmDelete = (studentId: number) => {
  console.log("确认!")
  emits('delete-student', studentId)
}
const cancelEvent = () => {
  console.log("取消");
}

</script>

<style scoped>
.table-container {
  overflow: auto;
}

.custom-pagination {
  justify-content: flex-end;
  display: flex;
  background: #fff;
  padding: 8px 0;
}

.custom-pagination :deep(.el-pager li) {
  border-radius: 6px;
  min-width: 32px;
  height: 32px;
  line-height: 32px;
  margin: 0 2px;
  color: #606266;
  font-weight: 400;
  border: none;
  background: transparent;
  transition: all 0.2s;
}

.custom-pagination :deep(.el-pager li.is-active) {
  background: #409EFF;
  color: #fff;
  font-weight: 500;
  border: none;
}

.custom-pagination :deep(.el-pager li:hover) {
  background: #f4f8ff;
  color: #409EFF;
}

.custom-pagination :deep(button) {
  border-radius: 6px;
  min-width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #606266;
  transition: all 0.2s;
}

.custom-pagination :deep(button:hover) {
  background: #f4f8ff;
  color: #409EFF;
}
</style>