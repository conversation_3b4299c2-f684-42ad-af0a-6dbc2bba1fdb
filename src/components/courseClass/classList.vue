<template>
  <div class="list-sidebar">
    <div class="list-top">
        <span>班级列表</span>
        <el-button type="primary" size="small" @click="$emit('add-class')" class="new-btn">新建</el-button>
    </div>
    <div class="list-container">
      <el-menu class="custom-class-menu" :default-active="selectedClass?.id.toString()" @select="onSelect">
        <el-menu-item
          v-for="cls in classes"
          :key="cls.id"
          :index="String(cls.id)"
          class="flex-item-container has-dot"
        >
          <span class="class-name">{{ cls.name }}</span>
          <el-dropdown class="actions-dropdown" placement="bottom-end">
            <img src="@/assets/icon/dotthreeIcon.png" alt="自定义图标" class="dotthree-icon" style="width: 1em; height: 1em; fill: currentColor;" />
            <!-- <el-button :icon="MoreFilled" size="mini" circle class="icon-btn"/> -->
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <el-dropdown-item :icon="Edit" @click="$emit('rename-class', cls.id)">重命名</el-dropdown-item> -->
                <el-dropdown-item @click="$emit('settings-class', cls.id)">
                  <img src="@/assets/icon/settings.svg" alt="自定义图标" style="width: 1em; height: 1em; fill: currentColor;" />
                  <span style="margin-left: 5px;">班级设置</span>
                </el-dropdown-item>
                <el-dropdown-item @click="$emit('delete-class', cls.id)">
                  <img src="@/assets/icon/delete-circle.svg" alt="自定义图标" style="width: 1em; height: 1em; fill: currentColor;" />
                  <span style="margin-left: 5px;">删除</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from 'vue'
import { MoreFilled, Operation, CircleClose } from '@element-plus/icons-vue'


export interface ClassType{
  id: number
  name: string
  semester: string
}

const props = defineProps<{ classes: ClassType[], selectedClass: ClassType | null }>()
const emits = defineEmits(['add-class', 'rename-class', 'settings-class', 'delete-class', 'update:selectedClass'])

function onSelect(id: string) {
  const cls = props.classes.find(c => c.id.toString() === id)
  emits('update:selectedClass', cls)
}

</script>

<style scoped>
/* 左侧列表区域 */
.list-sidebar {
  width: 300px;
  margin: 20px;
}

/* 列表顶部 */
.list-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

/* 列表容器 */
.list-container {
  overflow-y: auto;
  margin: 10px 0 10px 0;
  height: 700px;
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #e5e6eb #fff; /* Firefox */
}

/* Chrome/Edge/Safari */
.list-container::-webkit-scrollbar {
  width: 6px;
  background: #fff;
}
.list-container::-webkit-scrollbar-thumb {
  background: #e5e6eb;
  border-radius: 6px;
}
.list-container::-webkit-scrollbar-track {
  background: #fff;
}

/* 自定义菜单 */
.custom-class-menu {
  border-right: none;
}

/* 菜单项容器 */
.flex-item-container {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  height: 40px !important;
  margin-bottom: 5px !important;
}

/* 班级名称 */
.has-dot::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3f8cff;
  margin-right: 8px;
  vertical-align: middle;
}

/* 左侧数据区域 */
.class-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;/* 防止长名称溢出 */
  white-space: nowrap;
}

/* 右侧按钮区域 */
.actions-dropdown {
  margin-left: 10px;
}
/* 自定义按钮样式 */
.icon-btn {
  width: 14px !important;
  height: 14px !important;
  font-size: 10px !important;
  margin-left: 0px !important;
  padding: 0;
}

/* 自定义按钮样式 */
.icon-btn .el-icon {
  margin-right: 0px !important;
  font-size: 10px !important;
}

/* 更全面的图标样式覆盖 */
.icon-btn :deep(.el-icon),
.icon-btn :deep(svg),
.icon-btn :deep(i) {
  margin: 0px !important;
  font-size: 10px !important;
  width: 10px !important;
  height: 10px !important;
}

/* 选中样式 */
:deep(.el-menu-item.is-active) {
  background: #eaf4ff !important;
  border-radius: 8px !important;
}

/* 悬停样式 */
:deep(.el-menu-item:hover) {
  background: #eaf4ff !important;
  border-radius: 8px !important;
  color: #3f8cff !important;
}

/* 下拉菜单 */
:deep(.el-dropdown-menu) {
  border-radius: 10px !important;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12) !important;
  background: #fff !important;
  padding: 8px 0 !important;
  min-width: 120px !important;
  border: none !important;
}

/* 下拉菜单项 */
:deep(.el-dropdown-menu__item) {
  border-radius: 6px !important;
  margin: 0 8px !important;
  padding: 8px 12px !important;
}

/* 下拉菜单项悬停 */
:deep(.el-dropdown-menu__item:hover) {
  background: #eaf4ff !important;
  color: #3f8cff !important;
}

.new-btn {
  /* width: 68px; */
  /* height: 32px; */
  opacity: 1;
  border-radius: 3px;
  background: rgba(63, 140, 255, 1);
  border: none;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
}

.dotthree-icon {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.dotthree-icon:focus, .dotthree-icon:active {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

</style>