<template>
  <div class="class-manager">
    <!-- 班级列表 -->
    <ClassList
      :classes="classes"
      :selectedClass="selectedClass"
      @add-class="handleAddClass"
      @settings-class="handleSetClass"
      @delete-class="handleDeleteClass"
      @update:selectedClass="handleFetchClassStudents"
    />

    <!-- 分割线 -->
    <Divider
      type="vertical"
      style="height: 100%; color: #f0f0f0; background-color: #f0f0f0; border-color: #f0f0f0;"
    />

    <!-- 班级学生列表 -->
    <div class="main-content">
      <!-- 班级名称 -->
      <div class="header-title">
        <!-- <el-icon><Management /></el-icon> -->
        <img src="@/assets/icon/label.svg" alt="自定义图标" style="width: 1em; height: 1em; fill: currentColor;" />
        <span>{{ selectedClass?.name }}</span>
      </div>
      <!-- 班级操作 -->
      <div class="header">
        <!-- 搜索 -->
        <div>
            <el-input
              class="search-input-round search-input-32"
              v-model="searchValue"
              style="width: 280px; min-height: 32px; height: 32px; margin: 0 16px 0 10px;"
              placeholder="请输入"
            >
              <template #suffix>
                <img src="@/assets/icon/search.svg" alt="搜索" style="width: 1em; height: 1em;" />
              </template>
            </el-input>
            共有<span style="color: #5c89ff;">{{ filteredStudents.length }}</span>个筛选结果
        </div>
        <!-- 班级操作按钮 -->
        <div>
          <el-button
            class="remove-btn btn-32"
            plain
            size="default"
            :disabled="!selectedRows.length"
            @click="openRemoveStus"
          >
            <img
              src="@/assets/icon/delete-box.svg"
              alt="自定义图标"
              style="width: 1em; height: 1em; fill: currentColor;"
              :style="{ filter: !selectedRows.length ? 'grayscale(1) brightness(1.5)' : 'none' }"
            />
            <span style="margin-left: 5px;">移除</span>
          </el-button>
          <el-dropdown placement="bottom-start">
            <el-button type="primary" size="default" class="gradient-btn btn-32">添加学生</el-button>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item @click="handleManualImport">
                      <img src="@/assets/icon/manual-import.svg" alt="自定义图标" style="width: 1em; height: 1em; fill: currentColor;" />
                      <span style="margin-left: 5px;">手动导入</span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dialog v-model="dialogImportFormVisible" title="手动导入学生" width="500">
            <el-form :model="formImport" :rules="importRules" ref="importFormRef">
              <el-form-item label="导入学号" prop="studentId" :label-width="formLabelWidth">
                <el-input type="textarea" :rows="10" v-model="formImport.studentId" autocomplete="off" />
                <!-- <el-icon><Warning /></el-icon> -->
                <img src="@/assets/icon/tips-black.svg" alt="自定义图标" style="width: 1em; height: 1em; fill: currentColor;" /> 
                <span style="margin-left: 5px;">每个学号一行，可多行输入</span>
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button size="default" @click="dialogImportFormVisible = false">取 消</el-button>
                <el-button size="default" type="primary" @click="handleImportStudents">
                  确 定
                </el-button>
              </div>
            </template>
          </el-dialog>
        </div>
      </div>
      <!-- 班级学生列表 -->
      <StudentTable
        :students="filteredStudents"
        :current-page="currentPage"
        :total="students.length"
        @delete-student="handleDeleteStudent"
        @page-change="handlePageChange"
        @selection-change="handleSelectionChange"
      />
    </div>

    <!-- 新建班级弹框 -->
    <el-dialog v-model="dialogFormVisible" title="新建班级" width="500">
        <el-form ref="ruleNewClassFormRef" :rules="formClassRules" :model="formClass">
          <el-form-item label="班级名称：" prop="name" :label-width="formLabelWidth">
            <el-input v-model="formClass.name" autocomplete="off" />
          </el-form-item>
          <el-form-item label="班级学期：" prop="semester" :label-width="formLabelWidth">
            <el-select v-model="formClass.semester">
              <el-option label="Zone No.1" value="shanghai" />
              <el-option label="Zone No.2" value="beijing" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelHandleNewClass()">取 消</el-button>
            <el-button type="primary" @click="handleNewClass(ruleNewClassFormRef)">
              确 定
            </el-button>
          </div>
        </template>
    </el-dialog>

    <!-- 班级设置弹框 -->
    <el-dialog v-model="dialogClsSetFormVisible" title="班级设置" width="500">
        <el-form ref="ruleSetClassFormRef" :rules="formClassRules" :model="formClassSettings">
          <el-form-item label="班级名称：" prop="name" :label-width="formLabelWidth">
            <el-input v-model="formClassSettings.name" autocomplete="off" />
          </el-form-item>
          <el-form-item label="班级学期：" prop="semester" :label-width="formLabelWidth">
            <el-select v-model="formClassSettings.semester">
              <el-option label="Term No.1" value="第一学期" />
              <el-option label="Term No.2" value="第二学期" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelSetClass()">取 消</el-button>
            <el-button type="primary" @click="handleSettingsClass(ruleSetClassFormRef)">
              确 定
            </el-button>
          </div>
        </template>
    </el-dialog>

    <!-- 删除班级弹框 -->
    <el-dialog
      v-model="deleteClassDialogVisible"
      title="提示"
      width="400"
      align-center
    >
      <!-- <el-icon style="font-size: 18px; color: #f6be18;"><Warning /></el-icon> -->
      <div class="deleteDialog-flex">
        <img src="@/assets/icon/ExclamationCircle.png" alt="自定义图标" style="width: 1.1em; height: 1.1em;" />
        <span style="padding: 0 5px;">确定删除该班级记录吗？删除后无法恢复！</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="default" @click="deleteClassDialogVisible = false">取 消</el-button>
          <el-button type="danger" size="default" @click="deleteClassDialogVisible = false">
            删 除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除学生数据弹框 -->
    <el-dialog
      v-model="deleteStuDialogVisible"
      title="提示"
      width="400"
      align-center
    >
      <!-- <el-icon style="font-size: 18px; color: #f6be18;"><Warning /></el-icon> -->
      <!-- <span style="padding: 0 5px;">确定删除该学生记录吗？删除后无法恢复！</span> -->
      <div class="deleteDialog-flex">
        <img src="@/assets/icon/ExclamationCircle.png" alt="自定义图标" style="width: 1.1em; height: 1.1em;" />
        <span style="padding: 0 5px;">确定删除该学生记录吗？删除后无法恢复！</span>
      </div>      
      <template #footer>
        <div class="dialog-footer">
          <el-button size="default" @click="deleteStuDialogVisible = false">取 消</el-button>
          <el-button type="danger" size="default" @click="handleDeleteStus">
            删 除
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Management, Search, Delete, Document, Warning } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import ClassList from './classList.vue'
import StudentTable from './studentTable.vue'
import type { StudentType } from './studentTable.vue'
import type { ClassType } from './classList.vue'
import { Divider } from 'ant-design-vue'

// 状态定义
const dialogFormVisible = ref(false) // 新建班级弹框
const dialogClsSetFormVisible = ref(false) // 班级设置弹框
const deleteClassDialogVisible = ref(false) // 是否确定删除班级弹框
const deleteStuDialogVisible = ref(false) // 是否确定删除学生弹框
const dialogImportFormVisible = ref(false) // 手动导入学生学号弹框
const searchValue = ref('') // 搜索
const selectedRows = ref<StudentType[]>([])
const currentPage = ref(1)

// 表单数据
const formClass = reactive({
  name: '',
  semester: ''
})
const formClassSettings = reactive({
  name: '',
  semester: ''
})
const formImport = reactive({
  studentId: ''
})

// 班级数据
const classes = ref<ClassType[]>([])
// 班级学生列表数据
const students = ref<StudentType[]>([])

const selectedClass = ref<ClassType | null>(null)

// 计算属性：筛选学生
const filteredStudents = computed(() => {
  if (!searchValue.value) return students.value
  return students.value.filter(student =>
    student.name.includes(searchValue.value) ||
    student.studentId.includes(searchValue.value)
  )
})

// 表单验证规则
const formLabelWidth = '140px'
interface RuleForm {
  name: string
  semester: string
}
const formClassRules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入班级名称', trigger: 'blur' },
    { min:2, max: 20, message: '长度在2-20之间', trigger: 'blur' },
  ],
  semester: [
    { required: true, message: '请选择班级学期', trigger: 'blur' }
  ]
})
// 表单验证规则——导入学号
const importRules = reactive<FormRules>({
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入学号'))
          return
        }
        // 验证长度
        if (value.length > 1000) {
          callback(new Error('输入内容不能超过1000个字符'))
          return
        }
        // 验证字符类型（只允许数字、逗号和回车）
        const invalidChars = value.match(/[^0-9,\n\r]/g)
        if (invalidChars) {
          callback(new Error(`包含非法字符：${invalidChars.join('')}`))
          return
        }
        callback()
      },
      trigger: 'blur'
    }
  ]
})

// 表单引用
const ruleNewClassFormRef = ref<FormInstance>() // 新建班级表单
const ruleSetClassFormRef = ref<FormInstance>() // 班级设置表单
const importFormRef = ref<FormInstance>() // 导入学号表单

// 表格选择变更处理
const handleSelectionChange = (selection: StudentType[]) => {
  selectedRows.value = selection
}

// 删除选中学生(多个)
const openRemoveStus = () => {
  if (selectedRows.value.length === 0) return
  deleteStuDialogVisible.value = true
}
// 确定删除选中学生（多个）
const handleDeleteStus = () => {
  // 调用API删除
  console.log('删除选中的学生：', selectedRows.value.map(row => row.id))
  // 模拟删除操作
  students.value = students.value.filter(
    students => !selectedRows.value.some(row => row.id === students.id)
  )
  selectedRows.value = []
  deleteStuDialogVisible.value = false
}

// 打开新建班级弹框
function handleAddClass() {
  dialogFormVisible.value = true
}
// 打开班级设置弹框
const handleSetClass = () => {
  if (selectedClass.value) {
    formClassSettings.name = selectedClass.value.name
    formClassSettings.semester = selectedClass.value.semester
    dialogClsSetFormVisible.value = true
  }
}

// 取消新建班级
function cancelHandleNewClass() {
  console.log("取消新建班级", formClass.name, formClass.semester)
  formClass.name = ''
  formClass.semester = ''
  dialogFormVisible.value = false
}

const handleNewClass = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 调用新建班级接口新建班级，新建成功后关闭对话框。
      console.log('提交新建班级', formClass)
      handleFetchClasses()
      selectedClass.value = classes.value[0]
      dialogFormVisible.value = false
    } else {
      console.log('验证失败!', fields)
    }
  })
}

// 取消班级设置弹框
function cancelSetClass() {
  console.log("取消班级设置", formClassSettings.name, formClassSettings.semester)
  formClassSettings.name = ''
  formClassSettings.semester = ''
  dialogClsSetFormVisible.value = false
}

// 班级设置
const handleSettingsClass = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 调用新建班级接口新建班级，新建成功后关闭对话框。
      console.log('提交新建班级')
      handleFetchClasses()
      selectedClass.value = classes.value[0]
      dialogFormVisible.value = false
    } else {
      console.log('验证失败!', fields)
    }
  })
}

// 单行删除学生逻辑
const handleDeleteStudent = (studentId: number) => {
  // 调用API删除学生
  console.log('删除学生', studentId)
  students.value = students.value.filter(student => student.id !== studentId)
}

// 分页逻辑
function handlePageChange(page: number) {
  currentPage.value = page
}

// 手动添加学生逻辑
function handleManualImport() {
  dialogImportFormVisible.value = true
}
// 处理导入学生
const handleImportStudents = () => {
  importFormRef.value?.validate((valid) => {
    if (valid) {
      // 处理导入逻辑
      const studentIds = formImport.studentId
        .split(/[\n\r,]+/) // 按换行符或逗号分割
        .map(id => id.trim())
        .filter(id => id) // 过滤空字符串

      console.log('导入的学号:', studentIds)

      // 模拟导入学生
      const newStudents = studentIds.map((id, index) => ({
        id: Date.now() + index,
        name: `学生${id}`,
        studentId: id,
        department: selectedClass.value?.name || '计算机学院',
        major: '计算机科学与技术'
      }))

      students.value = [...students.value, ...newStudents]
      dialogImportFormVisible.value = false
      formImport.studentId = ''
    } else {
      console.log('验证失败')
      // return false
    }
  })
}

// 删除班级逻辑
function handleDeleteClass() {
  deleteClassDialogVisible.value = true
}

function handleFetchClasses() {
  classes.value = [
    { id: 1, name: '22级计算机科学与技术', semester: '第一学期' },
    { id: 2, name: '22级计算机应用技术', semester: '第二学期' }
  ]
  selectedClass.value = classes.value.length > 0 ? classes.value[0] : null

  console.log("selectedClass", selectedClass.value)
}

// 班级学生列表部分
function handleFetchClassStudents(selectedClass: ClassType | null) {
    // 调用接口获取班级学生列表。
    students.value = []
    if (selectedClass) {
        if (selectedClass.name === "22级计算机科学与技术") {
            students.value.push({ id: 1, name: '张一', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 2, name: '王', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 3, name: '李', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 4, name: '唐', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 5, name: '李', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 6, name: '阳', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 7, name: '格', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 8, name: '术', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 9, name: '计', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术' })
            students.value.push({ id: 10, name: '王', studentId: '1212121212121212', department: '计算机xxx', major: '计算机科学与技术'})
        } else if (selectedClass.name === "22级计算机应用技术") {
            students.value.push({ id: 1, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 2, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 3, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 4, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 5, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 6, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 7, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 8, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 9, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术' })
            students.value.push({ id: 10, name: 'xxx', studentId: '1783807018347017', department: '计算机xxx', major: '计算机应用技术'})
        }
    }
}

watch(selectedClass, (newValue, _oldValue) => {
    handleFetchClassStudents(newValue)
});

// 生命周期钩子
onMounted(() => {
    handleFetchClasses()
    handleFetchClassStudents(selectedClass.value)
})

</script>

<style scoped>
/* 班级管理 */
.class-manager {
  display: flex;
  max-height: 90%;
  position: relative;
  background-color: #ffffff;
  border-radius: 5px;
  overflow: auto;
}

/* 班级学生列表 */
.main-content {
  flex: 1;
  padding: 0 5px 0 5px;
  margin: 20px;
  /* border-left: 1px solid #999; */
  max-width: 100%;
  width: 75%;
  overflow: auto;
  position: relative;
  z-index: 1;
}

/* 班级名称 */
.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px 0 8px;
  font-weight: bold;
}

/* 班级操作 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
}

/* 分割线 */
:deep(.ant-divider-vertical) {
  /* z-index: 0 !important; */
  position: absolute !important;
  left: 340px !important;
  top: 0 !important;
  height: 100% !important;
  width: 2px !important;
  margin: 0px !important;
}

/* 搜索框 */
.search-input-round :deep(.el-input__wrapper) {
  border-radius: 999px !important;
  border: 1px solid #f0f0f0 !important;
  background: #fff !important;
  box-shadow: none !important;
}

/* 移除按钮 */
.remove-btn {
  color: #409EFF !important;
  border: 1px solid #409EFF !important;
  background: #fff !important;
  margin-right: 10px;
}

/* 移除按钮禁用 */
.remove-btn:disabled {
  color: #c0c4cc !important;
  border: 1px solid #ebeef5 !important;
  background: #fff !important;
}

/* 添加学生按钮设置渐变 */
.gradient-btn {
  background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
  border: none;
  color: white;
}

/* 下拉菜单 */
:deep(.el-dropdown-menu) {
  border-radius: 10px !important;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12) !important;
  background: #fff !important;
  padding: 8px 0 !important;
  min-width: 120px !important;
  border: none !important;
}

/* 下拉菜单项 */
:deep(.el-dropdown-menu__item) {
  border-radius: 6px !important;
  margin: 0 8px !important;
  padding: 8px 12px !important;
}

/* 下拉菜单项悬停 */
:deep(.el-dropdown-menu__item:hover) {
  background: #eaf4ff !important;
  color: #3f8cff !important;
}
/* 班级列表删除弹框，提示内容 */
.deleteDialog-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.search-input-32 :deep(.el-input__wrapper) {
  height: 32px !important;
  min-height: 32px !important;
  box-sizing: border-box;
}
.btn-32 {
  height: 32px !important;
  min-height: 32px !important;
  box-sizing: border-box;
  line-height: 32px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
/* 按钮聚焦、激活、悬停样式 */
:deep(.gradient-btn:focus),
:deep(.gradient-btn:active),
:deep(.gradient-btn:hover) {
  outline: none !important;
  box-shadow: none !important;
}
</style>