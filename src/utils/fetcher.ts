import { createFetch } from 'ofetch'

export const modelsFetcher = createFetch({
  defaults: {
    headers: {
      'Content-Type': 'application/json',
    },
    baseURL: '/api/api/v1/model-manager',
    responseType: 'json',
    onRequest: ({ options }) => {
      options.headers.set('X-CSRFToken', getCSRFToken())
    },
    onResponse: authOfetchInterceptor.responseSuccess,
    onResponseError: [
      authOfetchInterceptor.responseError,
      ({ response, error }) => {
        const message = response._data?.error ?? response._data?.message
        if (message) {
          throw new Error(message)
        }
        throw error
      },
    ],
    retry: false,
  },
})

export const kbFetcher = createFetch({
  defaults: {
    headers: {
      'Content-Type': 'application/json',
    },
    baseURL: '/api/api/v1',
    responseType: 'json',
    onRequest: ({ options }) => {
      options.headers.set('X-CSRFToken', getCSRFToken())
    },
    onResponse: authOfetchInterceptor.responseSuccess,
    onResponseError: [
      authOfetchInterceptor.responseError,
      ({ response, error }) => {
        const message = response._data?.error ?? response._data?.message
        if (message) {
          throw new Error(message)
        }
        throw error
      },
    ],
    retry: false,
  },
})
