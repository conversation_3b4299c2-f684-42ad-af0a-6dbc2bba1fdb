import { ElMessage, ElMessageBox } from 'element-plus'
import type { Action } from 'element-plus'

export const openMessage = (message: string, type: string) => {
    ElMessage({
        message: message as string,
        type: type as 'success' | 'warning' | 'info' | 'error',
    })
}

export const openMessageBox = (message: string, title: string) => {
    return new Promise((resolve, reject) => {
        ElMessageBox.confirm(message, title, {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        }).then(() => {
            resolve('confirmed');
        }).catch(() => {
            reject('canceled');
        });
    });
}

//题目难度等级转换难度:
// (0，'简单')，(1，'中等')，(2，'困难')，默认难度简单
export const levelTransform = (level: number) => {
    switch (level) {
        case 0:
            return '简单';
        case 1:
            return '中等';
        case 2:
            return '困难';
        default:
            return '简单';
    }
}

const Ens = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"];
// 根据索引获取字母
export const getEn = (index: number) => {
    return Ens[index] || ""; // 如果索引超出范围，返回空字符串
};

export const questionTypeData = [
    {
      value: '所有题型',
      label: '所有题型',
      
    },
    {
      value: '单选题',
      label: '单选题',
      
    },{
      value: '多选题',
      label: '多选题',
      
    },{
      value: '判断题',
      label: '判断题',
    },{
      value: '填空题',
      label: '填空题',
    },{
      value: '问答题',
      label: '问答题',
    }
  ]


export function formatDate(date: Date | string, format = 'YYYY-MM-DD HH:mm:ss') {
  const d = new Date(date);

  const pad = (n: number) => n.toString().padStart(2, '0');

  const year = d.getFullYear().toString();
  const month = pad(d.getMonth() + 1);
  const day = pad(d.getDate());
  const hours = pad(d.getHours());
  const minutes = pad(d.getMinutes());
  const seconds = pad(d.getSeconds());

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}