<template>
    <div class="" style="background: #F0F9FF;">
        <div class="flex top items-center px-[20px] justify-between">
            <div class="flex items-center cursor-pointer w-[60px] text-[#333]"
                @click="() => router.push(`/course/chapter/${$route.query.courseId}`)">
                <img class="w-[12px] h-[11px] mr-[8px]" src="@/assets/image/ArrowLeft.svg" />
                <span class="text-[14px] leading-[14px]">返回</span>
            </div>
            <div
                class="title flex items-center  w-[calc(100%-182px)] justify-center text-[20px] font-bold leading-[12px]">
                章节预览</div>
            <a-button class="w-[82px] h-[32px]"
                style="background: linear-gradient(135.84deg, #219FFF 0%, #0066FF 100%);color: #fff;"
                @click="editChapter">
                编辑
            </a-button>
        </div>

        <div class="pt-[60px] flex">
            <div
                class="shrink-[0] w-[351px] shadow-[0_2px_10px_#438ffe19] bg-[#fff] h-[calc(100vh-60px)] py-[30px] px-[20px] overflow-y-hidden">
                <div class="overflow-auto h-[calc(100vh-60px)] hide-scrollbar mb-[20px]">
                    <div
                        class="text-[16px] font-bold leading-[16px] border-l-[4px] border-solid border-[#3F8CFF] pl-[10px]">
                        目录
                    </div>
                    <div class="bg-white rounded-[0.4px] pt-[20px]" v-for="item in chapter">
                        <div class=" flex justify-between pr-[20px]" :class="{ 'mb-[20px]': item.open }">
                            <div class="flex items-center gap-[10px]">
                                <div class="w-[14px] cursor-pointer relative" @click="item.open = !item.open">
                                    <CaretDownOutlined v-if="item.open" />
                                    <CaretRightOutlined v-else />
                                    <div v-if="item.open"
                                        class="absolute left-1/2 top-full  w-px bg-[#E5E5E5] -translate-x-1/2"
                                        :style="{ height: `calc(66px * ${item.children.length})` }"></div>
                                </div>
                                <img src="@/assets/image/course/analysis.png" class="w-[17px] h-[17px]">
                                <div class="font-bold text-[16px] leading-[16px] line-clamp-[1] flex-1 cursor-pointer"
                                    @click="item.open = !item.open;checkedIdchildren='';getChapterDetail(item.id)">
                                    <a-tooltip placement="bottom">
                                        <template #title>
                                            {{ item.title }}
                                        </template>
                                        {{ item.title }}
                                    </a-tooltip>

                                </div>
                                <!-- <div class="font-bold text-[16px] leading-[16px] line-clamp-[1] cursor-pointer">{{ item.title }}</div> -->
                            </div>
                        </div>



                        <div v-if="item.open && item.children.length > 0"
                            class=" bg-[rgba(204,204,204,0.07)] p-[20px] ml-[22px] mt-[10px] border border-[#F2F2F2] rounded-[5px] relative cursor-pointer"
                            v-for="i in item.children"  @click="handleChildren(i.id)">
                            <div
                                class="absolute left-[-16px] top-1/2 h-px w-[16px] bg-[#E5E5E5] before:absolute before:content-[''] before:left-0 before:top-1/2 before:w-full before:h-px before:bg-[#E5E5E5]">
                            </div>

                            <div class="flex items-center justify-between h-[14px]">
                                <div class="flex items-center gap-[10px]">
                                    <div class="w-[10px] h-[10px] bg-[#3F8CFF] rounded-[50%] shrink-[0] "></div>
                                    <!-- #3F8CFF -->
                                    <div class="font-medium text-[14px] leading-[14px] line-clamp-[1]"
                                        :class="checkedIdchildren == i.id ? 'text-[#3F8CFF]' : ''">{{ i.title }}</div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div
                class="bg-white rounded-[10px] shadow-[0_2px_10px_#438ffe19] mt-[38px] mx-[180px] w-[100%] py-[38px] px-[100px] h-[calc(100vh-98px)] overflow-y-hidden">
                <div class="overflow-auto h-[calc(100vh-98px)] hide-scrollbar pb-[50px]">
                    <div class="text-[20px] font-bold leading-[20px] text-center mb-[16px]">
                        {{ chapterDetails.title }}<br>
                        <!-- {{ chapterDetails }} -->
                    </div>
                    <div
                        class="text-[#666] text-[14px] leading-[16px] text-center mt-[16px] border-b-[1px] border-[#E8E8E8] pb-[16px]">
                        课程名：<span class="text-[#333333]">计算机网络</span>
                    </div>
                    <!-- 教学目标 -->
                    <div class="bg-[rgba(204,204,204,0.05)] p-[20px] mt-[62px] ">
                        <div
                            class="chapter-bg flex items-center justify-center font-bold text-[18px] leading-[12px] text-[#333333]  w-[140px]">
                            教学目标
                        </div>
                        <div>
                            <!-- <div
                                class="mt-[43px] text-[16px] font-bold leading-[16px] border-l-[4px] border-solid border-[#3F8CFF] pl-[10px]">
                                知识与技能
                            </div> -->
                            <div class="mt-[17px] text-[14px] text-[#666] leading-[23px]">
                                {{ chapterDetails.content }}
                            </div>
                        </div>
                    </div>
                    <div class="bg-[rgba(135,197,255,0.05)] p-[20px] mt-[20px]">
                        <div
                            class="chapter-bg flex items-center justify-center font-bold text-[18px] leading-[12px] text-[#333333]  w-[106px]">
                            教案
                        </div>
                        <div class="flex items-center relative" @mouseenter="isJiaoanHovered = true"
                            @mouseleave="isJiaoanHovered = false">
                            <img v-if="chapterScroll.showArrows.value && isJiaoanHovered"
                                @click="chapterScroll.scrollTo('left')" src="@/assets/image/img/leftnex.png"
                                class="absolute left-0 w-[29px] h-[29px] transform -translate-x-1/2 z-10">
                            <div class="flex-1 min-w-0 overflow-hidden" ref="chapterWrapper">
                                <div class="flex overflow-auto hide-scrollbar gap-[20px] whitespace-nowrap"
                                    ref="chapterContainer">
                                    <div class="jiaoan mt-[20px] pt-[25px] shrink-[0]" v-for="item in chapterDetails.teach_plans">
                                        <div class="bg-[linear-gradient(90deg,#15C0E6_0.69%,#0F7FFF_100%)] h-[30px] text-center text-[12px] leading-[12px] text-white
                                        flex items-center justify-center w-[100%]
                                    ">
                                            {{ item.title }}
                                            <!-- 教案名称{{ item }}<br>
                                            XXXXXXXXXXXX -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <img v-if="chapterScroll.showArrows.value && isJiaoanHovered"
                                @click="chapterScroll.scrollTo('right')" src="@/assets/image/img/rightnext.png"
                                class="absolute right-0 w-[29px] h-[29px] transform translate-x-1/2 z-10">
                        </div>
                    </div>
                    <div class="bg-[rgba(135,197,255,0.05)] p-[20px] mt-[20px]">
                        <div
                            class="chapter-bg flex items-center justify-center font-bold text-[18px] leading-[12px] text-[#333333]  w-[106px]">
                            PPT
                        </div>
                        <div class="flex items-center relative" @mouseenter="isPptHovered = true"
                            @mouseleave="isPptHovered = false">
                            <img v-if="resourceScroll.showArrows.value && isPptHovered"
                                @click="resourceScroll.scrollTo('left')" src="@/assets/image/img/leftnex.png"
                                class="absolute left-0 w-[29px] h-[29px] transform -translate-x-1/2 z-10">
                            <div class="flex-1 min-w-0 overflow-hidden" ref="resourceWrapper">
                                <div class="flex overflow-auto hide-scrollbar gap-[20px] whitespace-nowrap"
                                    ref="resourceContainer">
                                    <div class="mt-[20px] shrink-[0] relative" v-for="item in chapterDetails.ppts">
                                        <div class="absolute bottom-[3px] right-[3px] z-10 text-white text-[16px] leading-[16px] font-bold">
                                            {{ item.title }}
                                        </div>
                                        <img src="@/assets/image/img/ppt.png" class="w-[176px] h-[103px]">
                                    </div>
                                </div>
                            </div>

                            <img v-if="resourceScroll.showArrows.value && isPptHovered"
                                @click="resourceScroll.scrollTo('right')" src="@/assets/image/img/rightnext.png"
                                class="absolute right-0 w-[29px] h-[29px] transform translate-x-1/2 z-10">
                        </div>
                    </div>

                    <div class="bg-[rgba(135,197,255,0.05)] p-[20px] mt-[20px]">
                        <div
                            class="chapter-bg flex items-center justify-center font-bold text-[18px] leading-[12px] text-[#333333]  w-[106px]">
                            知识点</div>

                        <div class="py-[15px] pr-[20px] pl-[15px] bg-white mt-[16px] border border-[#DCE8FA] rounded-[3px]"
                            v-for="item in chapterDetails.knowledge_points">
                            <div class="cursor-pointer flex" @click="item.open = !item.open">
                                <CaretDownOutlined style="color: #3F8CFF;" v-if="item.open" />
                                <CaretRightOutlined style="color: #3F8CFF;" v-else />
                                <div class="text-[14px] text-[#333333] font-bold ml-[8px] shrink-[0] leading-[16px]">
                                    {{ item.name }}
                                </div>
                            </div>
                            <div class="mt-[7px] ml-[22px] text-[14px] text-[#666] leading-[23px]" v-if="item.open">
                                {{ item.content }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script lang="ts" setup>
import useHorizontalScroll from '@/hooks/scroll';
import { chapterList,chapterTables,chapterDetail } from '@/services/api/course';

import { CaretUpOutlined, CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()

const isJiaoanHovered = ref(false)
const isPptHovered = ref(false)

const checkedIdchildren = ref()
interface Chapter {
    id: number,
    title: string,
    progress: number,
    open: boolean,
    children: {
        id: number,
        title: string,
        content: string,
        progress: number,
    }[]
}
const chapter = ref<Chapter[]>([])




onMounted(async () => {
    getchapterTable()
    getChapterDetail('')

});
const chapterTablesId = route.query.chapterId//当前章id

async function getchapterTable(){
    const chapterTable = await chapterTables(chapterTablesId)
    chapter.value = []
    chapter.value.push(chapterTable.data) 
    chapter.value[0].open = true
    console.log(chapter.value, '章节目录')
}
const chapterId = ref(route.query.id)//当前章节id
const chapterDetails = ref<any>({})
function getChapterDetail(id: any) {
    chapterId.value = id || chapterId.value
    chapterDetail(chapterId.value).then((res: any) => {
        if (res.code == 200) {
            chapterDetails.value = res.data
            // chapterDetails.value.teach_plans = chapterDetails.value.teach_plans.map((item: any) => {
            //     return {
            //         ...item,
            //         value: item.id,
            //     }
            // })
        }
    });
}

function handleChildren(id: any) {
    if (checkedIdchildren.value == id) {
        checkedIdchildren.value = 0
        return
    }
    checkedIdchildren.value = id
    getChapterDetail(id)
}
function editChapter() {
    console.log('编辑章节',route)
    // return
    router.push({
        path: '/chapter/edit',
        query: {
            id: route.query.id,
            chapterId: route.query.chapterId,
            courseId: route.query.courseId
        }
    })
}


// 第一个滚动区域
const chapterWrapper = ref<HTMLElement | null>(null);
const chapterContainer = ref<HTMLElement | null>(null);
const chapterScroll = useHorizontalScroll(chapterWrapper, chapterContainer);
console.log(chapterScroll.showArrows.value, 'chapterScrollchapterScroll');

// 第二个滚动区域
const resourceWrapper = ref<HTMLElement | null>(null);
const resourceContainer = ref<HTMLElement | null>(null);
const resourceScroll = useHorizontalScroll(resourceWrapper, resourceContainer);
console.log(resourceScroll.showArrows.value);


</script>
<style lang="scss" scoped>
.top {
    height: 60px;
    opacity: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
    position: fixed;
    width: 100%;
    z-index: 9999;
}

.chapter-bg {
    background-image: url('@/assets/image/img/chaptertibg.png');
    background-size: 100% 100%;
    //   background-position: center;
    //   width: 140px;
    height: 14px;
}

.jiaoan {
    background-image: url('@/assets/image/img/jiaoanbg.png');
    background-size: 100% 100%;
    width: 120px;
    height: 155px;
}
</style>