<template>
    <div class="" style="background: #F0F9FF;">
        <div class="flex top items-center px-[20px] justify-between">
            <div class="flex items-center cursor-pointer w-[60px] text-[#333]" @click="() => router.go(-1)">
                <img class="w-[12px] h-[11px] mr-[8px]" src="@/assets/image/ArrowLeft.svg" />
                <span class="text-[14px] leading-[14px]">返回</span>
            </div>
            <div
                class="title flex items-center  w-[calc(100%-182px)] justify-center text-[20px] font-bold leading-[12px]">
                章节编辑</div>
            <a-button class="w-[82px] h-[32px]" @click="chapterEdited"
                style="background: linear-gradient(135.84deg, #219FFF 0%, #0066FF 100%);color: #fff;">
                完成
            </a-button>
        </div>

        <div class="pt-[60px] flex">
            <div
                class="w-[351px] shadow-[0_2px_10px_#438ffe19] bg-[#fff] h-[calc(100vh-60px)] py-[30px] px-[20px] overflow-y-hidden">
                <div class="overflow-auto h-[calc(100vh-60px)] hide-scrollbar mb-[20px]">
                    <div
                        class="text-[16px] font-bold leading-[16px] border-l-[4px] border-solid border-[#3F8CFF] pl-[10px]">
                        目录
                    </div>
                    <div class="bg-white rounded-[0.4px] pt-[20px]" v-for="item in chapter">
                        <div class="pr-[20px]" :class="{ 'mb-[20px]': item.open && item.children.length > 0 }">
                            <div class="flex items-center gap-[10px]">
                                <div class="w-[14px] cursor-pointer relative" @click="item.open = !item.open">
                                    <CaretDownOutlined v-if="item.open" />
                                    <CaretRightOutlined v-else />
                                    <div v-if="item.open"
                                        class="absolute left-1/2 top-full  w-px bg-[#E5E5E5] -translate-x-1/2"
                                        :style="{ height: `calc(66px * ${item.children.length})` }"></div>
                                </div>
                                <img src="@/assets/image/course/analysis.png" class="w-[17px] h-[17px]">

                                <div class="font-bold text-[16px] leading-[16px] line-clamp-[1] flex-1 cursor-pointer"
                                    @click="item.open = !item.open;checkedIdchildren='';getChapterDetail(item.id)">
                                    <a-tooltip placement="bottom">
                                        <template #title>
                                            {{ item.title }}
                                        </template>
                                        {{ item.title }}
                                    </a-tooltip>

                                </div>
                                <a-popover placement="bottomRight">
                                    <template #content>
                                        <div @click="handleAdd(1, item)"
                                            class="text-[#333] cursor-pointer rounded-[3px] leading-[14px] p-[10px] gap-[6px] hover:bg-[rgba(63,140,255,0.1)]">
                                            <PlusCircleOutlined />
                                            子目录
                                        </div>
                                        <div @click="handleAdd(0, item.id)"
                                            class="text-[#333] cursor-pointer rounded-[3px] leading-[14px] p-[10px] gap-[6px] hover:bg-[rgba(63,140,255,0.1)]">
                                            <CloseCircleOutlined />
                                            删除
                                        </div>
                                    </template>
                                    <img src="@/assets/icon/dotthreeIcon.png" class="w-[17px] h-[17px]">
                                </a-popover>
                            </div>
                        </div>


                        <div>
                            <div v-if="item.open && item.children.length > 0"
                                class=" bg-[rgba(204,204,204,0.07)] p-[20px] ml-[22px] mt-[10px] border border-[#F2F2F2] rounded-[5px] relative cursor-pointer"
                                v-for="i in item.children" @click="handleChildren(i.id)">
                                <div
                                    class="absolute left-[-16px] top-1/2 h-px w-[16px] bg-[#E5E5E5] before:absolute before:content-[''] before:left-0 before:top-1/2 before:w-full before:h-px before:bg-[#E5E5E5]">
                                </div>

                                <div class="flex items-center gap-[10px] h-[14px]">
                                    <div class="w-[10px] h-[10px] bg-[#3F8CFF] rounded-[50%] shrink-[0] "></div>
                                    <div class="font-medium text-[14px] leading-[14px] line-clamp-[1] flex-1"
                                        :class="checkedIdchildren == i.id ? 'text-[#3F8CFF]' : ''">
                                        {{ i.title }}
                                    </div>

                                    <a-popover placement="bottomRight">
                                        <template #content>
                                            <div @click="handleAdd(1, item)"
                                                class="text-[#333] cursor-pointer rounded-[3px] leading-[14px] p-[10px] gap-[6px] hover:bg-[rgba(63,140,255,0.1)]">
                                                <PlusCircleOutlined />
                                                同级目录
                                            </div>
                                            <!-- <div  @click="handleAdd(2,item)"
                                            class="text-[#333] cursor-pointer rounded-[3px] leading-[14px] p-[10px] gap-[6px] hover:bg-[rgba(63,140,255,0.1)]">
                                                <PlusCircleOutlined />
                                                子目录
                                            </div> -->
                                            <div @click="handleAdd(0, i.id)"
                                                class="text-[#333] cursor-pointer rounded-[3px] leading-[14px] p-[10px] gap-[6px] hover:bg-[rgba(63,140,255,0.1)]">
                                                <CloseCircleOutlined />
                                                删除
                                            </div>
                                        </template>
                                        <img src="@/assets/icon/dotthreeIcon.png" class="w-[17px] h-[17px]">
                                    </a-popover>
                                </div>
                            </div>
                            <div v-if="item.open && item.children.length == 0" class="text-center">
                                <img src="@/assets/image/zanwu/zanwuIcon.png" />
                                <div class="text-[#666666] text-[14px]">暂无章节目录</div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="bg-white rounded-[10px] shadow-[0_2px_10px_#438ffe19] mt-[38px] mx-[180px] 
             py-[38px] px-[100px] h-[calc(100vh-98px)] overflow-y-hidden">
                <div class="overflow-auto h-[calc(100vh-98px)] hide-scrollbar pb-[50px]">
                    <div class="text-[20px] font-bold leading-[20px] text-center mb-[16px]">
                        <div v-if="!blur" @click="blur = true">{{ chapterDetails.title }}</div>
                        <a-input v-model:value="chapterDetails.title" @blur="blur = false" @focus="blur = true"
                            v-else />
                    </div>
                    <div
                        class="text-[#666] text-[14px] leading-[16px] text-center mt-[16px] border-b-[1px] border-[#E8E8E8] pb-[16px]">
                        课程名：<span class="text-[#333333]">{{ courseDetails.title }}</span>
                    </div>
                    <!-- 教学目标 -->
                    <div class="bg-[rgba(204,204,204,0.05)] p-[20px] mt-[62px] ">
                        <div
                            class="chapter-bg flex items-center justify-center font-bold text-[18px] leading-[12px] text-[#333333]  w-[140px]">
                            教学目标
                        </div>
                        <div class="mt-[23px]">
                            <QuestionEditor :questionValue="chapterDetails.content" :key="chapterDetails.id"
                                @valueEditorChange="getchapterContent">
                            </QuestionEditor>
                            <!-- 富文本框 -->
                        </div>
                    </div>
                    <div class="bg-[rgba(135,197,255,0.05)] p-[20px] mt-[20px]">
                        <div class="flex items-center justify-between w-[100%]">
                            <div
                                class="chapter-bg flex items-center justify-center font-bold text-[18px] leading-[12px] text-[#333333]  w-[106px]">
                                教案
                            </div>
                            <div class="w-[76px] h-[24px] bg-[#3F8CFF] flex items-center justify-center rounded-[3px]">
                                <a-button type="text" @click="getTeachplanUnbind"
                                    style="font-size: 14px;font-weight: 500;line-height: 14px;color: #fff;">取消关联</a-button>
                            </div>
                        </div>
                        <div class="flex items-center relative" @mouseenter="isJiaoanHovered = true"
                            @mouseleave="isJiaoanHovered = false">
                            <img v-if="chapterScroll.showArrows.value && isJiaoanHovered"
                                @click="chapterScroll.scrollTo('left')" src="@/assets/image/img/leftnex.png"
                                class="absolute left-0 w-[29px] h-[29px] transform -translate-x-1/2 z-10">
                            <div class="flex-1 min-w-0 overflow-hidden" ref="chapterWrapper">
                                <div class="flex overflow-auto hide-scrollbar gap-[20px] whitespace-nowrap"
                                    ref="chapterContainer">
                                    <div class="jiaoan mt-[20px]  shrink-[0]" v-for="item in chapterDetails.teach_plans"
                                        :key="item.id">
                                        <div class="ml-[3px]">
                                            <a-checkbox v-model:value="item.id" @change="handleChangeTeachPlan"></a-checkbox>
                                        </div>
                                        <div class="bg-[linear-gradient(90deg,#15C0E6_0.69%,#0F7FFF_100%)] h-[30px] text-center text-[12px] leading-[12px] text-white
                                            flex items-center justify-center w-[100%]
                                        ">

                                            {{ item.title }}<br>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <img v-if="chapterScroll.showArrows.value && isJiaoanHovered"
                                @click="chapterScroll.scrollTo('right')" src="@/assets/image/img/rightnext.png"
                                class="absolute right-0 w-[29px] h-[29px] transform translate-x-1/2 z-10">
                        </div>
                    </div>
                    <div class="bg-[rgba(135,197,255,0.05)] p-[20px] mt-[20px]">
                        <div class="flex items-center justify-between w-[100%]">
                            <div
                                class="chapter-bg flex items-center justify-center font-bold text-[18px] leading-[12px] text-[#333333]  w-[106px]">
                                PPT
                            </div>
                            <div class="w-[76px] h-[24px] bg-[#3F8CFF] flex items-center justify-center rounded-[3px]">
                                <a-button type="text" @click="getPptUnbind"
                                    style="font-size: 14px;font-weight: 500;line-height: 14px;color: #fff;">取消关联</a-button>
                            </div>
                        </div>

                        <div class="flex items-center relative" @mouseenter="isPptHovered = true"
                            @mouseleave="isPptHovered = false">
                            <img v-if="resourceScroll.showArrows.value && isPptHovered"
                                @click="resourceScroll.scrollTo('left')" src="@/assets/image/img/leftnex.png"
                                class="absolute left-0 w-[29px] h-[29px] transform -translate-x-1/2 z-10">
                            <div class="flex-1 min-w-0 overflow-hidden" ref="resourceWrapper">
                                <div class="flex overflow-auto hide-scrollbar gap-[20px] whitespace-nowrap"
                                    ref="resourceContainer">
                                    <div class="mt-[20px] shrink-[0] relative" v-for="item in chapterDetails.ppts">
                                        <a-checkbox class="absolute top-[3px] left-[3px] z-10"  v-model:value="item.id"  @change="handleChangePPT" />
                                        <div class="absolute bottom-[3px] right-[3px] z-10 text-white text-[16px] leading-[16px] font-bold">
                                            {{ item.title }}
                                        </div>
                                        <img src="@/assets/image/img/ppt.png" class="w-[176px] h-[103px]">
                                    </div>
                                </div>
                            </div>

                            <img v-if="resourceScroll.showArrows.value && isPptHovered"
                                @click="resourceScroll.scrollTo('right')" src="@/assets/image/img/rightnext.png"
                                class="absolute right-0 w-[29px] h-[29px] transform translate-x-1/2 z-10">
                        </div>
                    </div>


                    <div class="bg-[rgba(135,197,255,0.05)] p-[20px] mt-[20px]">
                        <div class="flex items-center justify-between">
                            <div
                                class="chapter-bg flex items-center justify-center font-bold text-[18px] leading-[12px] text-[#333333]  w-[106px]">
                                知识点
                            </div>
                            <div class="text-[#3F8CFF] cursor-pointer"
                                @click="[openkonwledge = !openkonwledge, restKonwledge(), modalTitleKonwledge = '添加知识点']">
                                <PlusCircleOutlined style="color: #3F8CFF;" />
                                添加知识点
                            </div>
                        </div>

                        <div class="py-[15px] pr-[20px] pl-[15px] bg-white mt-[16px] border border-[#DCE8FA] rounded-[3px]"
                            v-for="item in chapterDetails.knowledge_points" @mouseenter="onEnter(item.id)"
                            @mouseleave="onLeave">


                            <div class="flex justify-between items-center">
                                <div class="cursor-pointer flex" @click="item.open = !item.open">
                                    <CaretDownOutlined style="color: #3F8CFF;" v-if="item.open" />
                                    <CaretRightOutlined style="color: #3F8CFF;" v-else />
                                    <div class="text-[14px] text-[#333333] font-bold ml-[8px] leading-[16px]">
                                        {{ item.name }}
                                    </div>
                                </div>
                                <div v-if="hoveredId == item.id">
                                    <a-button type="text" size="small" @click="editKonwledge(item)">
                                        <div class="flex items-center">
                                            <img src="@/assets/image/img/edittable.png" alt=""
                                                class="w-[14px] h-[14px]">
                                            <div class="ml-[3] text-[#3F8CFF] leading-[16px]">编辑</div>
                                        </div>
                                    </a-button>
                                    <a-button type="text" size="small" @click="deleteKonwledge(item.id)">
                                        <div class="flex items-center leading-[16px]">
                                            <img src="@/assets/image/img/reddel.png" alt="" class="w-[14px] h-[14px]">
                                            <div class="ml-[3] text-[#FA0000]">删除</div>
                                        </div>
                                    </a-button>
                                </div>
                            </div>

                            <div class="mt-[7px] ml-[22px] text-[14px] text-[#666] leading-[23px]" v-if="item.open">
                                {{ item.description }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <a-modal v-model:open="openkonwledge" width="700px" :footer="null" :title="modalTitleKonwledge">
            <a-form ref="formRef" :model="konwledge" layout="vertical">
                <a-form-item label="知识点名称" name="name" v-bind="validateInfos.name">
                    <div v-if="modalTitleKonwledge == '添加知识点'">
                        <QuestionEditor :questionValue="konwledge.name" @valueEditorChange="getKonwledegeName">
                        </QuestionEditor>
                    </div>
                    <div v-if="modalTitleKonwledge == '编辑知识点' && konwledge.name">
                        <QuestionEditor :questionValue="konwledge.name" @valueEditorChange="getKonwledegeName">
                        </QuestionEditor>
                    </div>
                </a-form-item>
                <a-form-item label="知识点描述" name="description"  v-bind="validateInfos.description">
                    <div v-if="modalTitleKonwledge == '添加知识点'">
                        <QuestionEditor :questionValue="konwledge.description" @valueEditorChange="getKonwledegeDes">
                        </QuestionEditor>
                    </div>
                    <div v-if="modalTitleKonwledge == '编辑知识点' && konwledge.description">
                        <QuestionEditor :questionValue="konwledge.description" @valueEditorChange="getKonwledegeDes">
                        </QuestionEditor>
                    </div>
                </a-form-item>
                <div class="flex justify-end">
                    <a-form-item>
                        <a-space>
                            <a-button @click=" openkonwledge = false">取消</a-button>
                            <a-button type="primary" html-type="submit" @click="onSubmit" style=""
                                :loading="loadingSubmit">确定</a-button>
                        </a-space>
                    </a-form-item>
                </div>
            </a-form>
        </a-modal>

    </div>
</template>

<script lang="ts" setup>
import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
const { courseDetails, chapterSide } = toRefs(courseStore)
import { Form } from 'ant-design-vue';
const useForm = Form.useForm;
import { teachplanUnbind, pptUnbind, knowledgeAdd, chapterDetail, chapterAdd, chapterTables, 
    chapterDelete, knowledgeDelete, knowledgeEdit, chapterEdit } from '@/services/api/course'



import { PlusCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
import QuestionEditor from "@/components/QuestionEditor.vue";
import useHorizontalScroll from '@/hooks/scroll';
import { useMouseHover } from '@/hooks/useMouseHover'
import { v4 as uuidv4 } from 'uuid'; // 导入uuid库
import { CaretUpOutlined, CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
console.log(route.query)

const teachplan_ids = ref<number[]>([])
const handleChangeTeachPlan = async (checkedValue: any) => {
    const id = checkedValue.target.value
    if(teachplan_ids.value.includes(id)){
        teachplan_ids.value = teachplan_ids.value.filter((item) => item !== id)
    }else{
        teachplan_ids.value.push(id)
    }
}

const getTeachplanUnbind = async () => {
    const res:any = await teachplanUnbind({
        "teachplan_ids":teachplan_ids.value   //传入需要取消绑定的教案id
    })
    if(res.code == 200){
        getChapterDetail('')
    }
}
const ppt_ids = ref<number[]>([])
const handleChangePPT = async (checkedValue: any) => {
    const id = checkedValue.target.value
    if(ppt_ids.value.includes(id)){
        ppt_ids.value = ppt_ids.value.filter((item) => item !== id)
    }else{
        ppt_ids.value.push(id)
    }
}

const getPptUnbind = async () => {
    const res:any = await pptUnbind({
        "ppt_ids":ppt_ids.value   //传入需要取消绑定的教案id
    })
    if(res.code == 200){
        getChapterDetail('')
    }
}


const blur = ref(false)
const modalTitleKonwledge = ref('添加知识点')


//知识点弹窗字段
interface konwledge {
    name: string;
    description: string;
    Chapter: number;
    open?: boolean;
    id?: number;
    content?: string;
}
const konwledge = ref<konwledge>({
    name: '',
    description: '',
    Chapter: 0
})

const restKonwledge = () => {
    konwledge.value.name = ''
    konwledge.value.description = ''
}
interface ChapterItem {
    id: number | string;
    course: number;
    title: string;
    content: string;
    parent: number | null;
    order: number;
    open: boolean;
    children: ChapterChild[]; // 子目录数组，可选
}

interface ChapterChild {
    id: number | string;
    title: string;
    content: string;
    parent: number | string;
    order: number;
}
const checkedIdchildren = ref()
const chapter = ref<ChapterItem[]>([])

const chapterTablesId = route.query.chapterId//当前章id
onMounted(() => {
    getchapterTable()
})
//获取目录
async function getchapterTable(){
    const chapterTable = await chapterTables(chapterTablesId)
    chapter.value = []
    chapter.value.push(chapterTable.data) 
    chapter.value[0].open = true
    console.log(chapter.value, '章节目录')
}
const chapterId = ref(isNaN(parseInt(route.query.id as string, 0)) ? 0 : parseInt(route.query.id as string, 0)); //const chapterIds = ref()//当前章节
konwledge.value.Chapter = chapterId.value;

const isJiaoanHovered = ref(false)
const isPptHovered = ref(false)
const loadingSubmit = ref(false)
const openkonwledge = ref(false)

const { hoveredId, onEnter, onLeave } = useMouseHover() // 

//新增目录
function handleAdd(level: number, item: any) {
    console.log('level', level, item)
    if (level == 1) { //一级目录
        chapter.value.forEach(async it => {
            if (it.id == item.id) {
                const newChapter = await chapterAdded('初始章节', item.id)
                item.children.push(newChapter)
            }
        })
    } else if (level == 2) {
        console.log('删除当前章节', chapter.value)
    } else if (level == 0) { //删除当前章节
        console.log('删除章节', item)
        delChapter(item)
        // chapter.value
        // chapter.value = chapter.value.filter(it=>it.id != item.id)
    }
}
async function delChapter(item: any) {
    const confirmed = await showDeleteConfirm('确定删除本章节吗？章节关联的内容将自动解除绑定！');
    if (confirmed) {
        chapterDelete(item).then((res: any) => {
            if (res.code == 200) {
                getchapterTable()
            }
        })
    }
}
//新增章节
async function chapterAdded(title: string, parent: number) {
    const params = {
        course: courseDetails.value.id,
        title,
        content: '',
        parent,
    }
    try {
        const res = await chapterAdd(params)
        return res.data //
    } catch (error) {
        console.error('新增章节失败:', error)
        throw error
    }
}

function handleChildren(id: any) {
    if (checkedIdchildren.value == id) {
        checkedIdchildren.value = 0
        return
    }
    checkedIdchildren.value = id
    getChapterDetail(id)
}

//章节编辑修改
function chapterEdited() {
    chapterEdit(chapterDetails.value).then(res => {
        console.log('章节修改成功', res)
    }).catch(err => {
        console.log('章节编辑失败', err)
    })
}


const { resetFields, validate, validateInfos } = useForm(konwledge, reactive({
    name: [
        {
            required: true,
            message: '请输入知识点名称!',
        },
    ],description: [
        {
            required: true,
            message: '请输入知识点描述!',
        },
    ]
}));//验证课程表单提交
const onSubmit = () => {
    validate().then(() => {
        loadingSubmit.value = true
        konwledge.value.Chapter = chapterId.value;
        chapterEdited()
        if (modalTitleKonwledge.value == '编辑知识点') {
            knowledgeEdit(konwledge.value).then((res: any) => {
                if (res.code == 200) {
                    openkonwledge.value = false
                    loadingSubmit.value = false
                    getChapterDetail('')
                }
            })
            return
        }
        knowledgeAdd(konwledge.value).then((res: any) => {
            if (res.code == 200) {
                openkonwledge.value = false
                loadingSubmit.value = false
                getChapterDetail('')
            }
        })
    }).catch((err: any) => {
        console.log('error', err);
    });
};

//章节详情
const chapterDetails = ref<any>({})//获取详情章节
function getChapterDetail(id: any) {
    chapterId.value = id || chapterId.value
    chapterDetail(chapterId.value).then((res: any) => {
        if (res.code == 200) {
            chapterDetails.value = res.data
            // chapterDetails.value.teach_plans = chapterDetails.value.teach_plans.map((item: any) => {
            //     return {
            //         ...item,
            //         value: item.id,
            //     }
            // })
        }
    });
}

//知识点编辑editKonwledge
function editKonwledge(item: any) {
    konwledge.value = JSON.parse(JSON.stringify(item)) // 深拷贝
    modalTitleKonwledge.value = '编辑知识点'
    openkonwledge.value = true
};
// 知识点删除
async function deleteKonwledge(id: any) {
    const confirmed = await showDeleteConfirm('确定删除本知识点吗？知识点关联的题目将自动解除绑定！');
    if (confirmed) {
        knowledgeDelete(id).then((res: any) => {
            if (res.code == 200) {
                getChapterDetail('')
            }
        })
    }
}
//


const getKonwledegeName = (item: any) => {
    konwledge.value.name = item.valueEditor
}
const getKonwledegeDes = (item: any) => {
    konwledge.value.description = item.valueEditor
}
const getchapterContent = (item: any) => {
    chapterDetails.value.content = item.valueEditor
}

getChapterDetail('')

// 第一个滚动区域
const chapterWrapper = ref<HTMLElement | null>(null);
const chapterContainer = ref<HTMLElement | null>(null);
const chapterScroll = useHorizontalScroll(chapterWrapper, chapterContainer);

// 第二个滚动区域
const resourceWrapper = ref<HTMLElement | null>(null);
const resourceContainer = ref<HTMLElement | null>(null);
const resourceScroll = useHorizontalScroll(resourceWrapper, resourceContainer);


</script>
<style lang="scss" scoped>
.top {
    height: 60px;
    opacity: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
    position: fixed;
    width: 100%;
    z-index: 9999;
}

.chapter-bg {
    background-image: url('@/assets/image/img/chaptertibg.png');
    background-size: 100% 100%;
    //   background-position: center;
    //   width: 140px;
    height: 14px;
}

.jiaoan {
    background-image: url('@/assets/image/img/jiaoanbg.png');
    background-size: 100% 100%;
    width: 120px;
    height: 155px;
}
</style>