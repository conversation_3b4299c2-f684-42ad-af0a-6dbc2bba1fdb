<template>
  <div class="page">
      <div class="toolbar">
        <div class="toolbar-text">
            <span>摘要提取</span>
        </div>
      </div>
      
      <div class="box-container">
        <div class="box-left" style="  flex: 7;">
            <div class="top">
              <a-select class="but" v-model:value="countSelect" placeholder="摘要长度" style="width: 360px; height: 50px;">
                  <a-select-option value = '50'>摘要长度-短</a-select-option>
                  <a-select-option value = '100'>摘要长度-长</a-select-option>
              </a-select>
              <a-upload :file-list="fileList" :maxCount="1" accept=".doc,.docx"  @remove="handleRemove" :before-upload="beforeUpload">
                <a-button class="but" style="width: 120px;">
                  上传附件<PaperClipOutlined />
                </a-button>
              </a-upload>
            </div>
            <div class="center">
              <div class="textarea" v-html="parseMarkdown(content)"></div>
            </div>
            <div class="bottom">
              <a-button type="primary" class="addbut" style="padding: 2px;" @click="getAbstract">
                <img src="@/assets/image/abstract/abstract.png"/>
                摘要提取
              </a-button>
            </div>

            
        </div>
        <div class="box-right" style="flex: 3;">
            <div class="text">
              <span>生成结果</span>
            </div>
            <div class="content">
              <a-spin :spinning="spinning">
                <div class="textarea" v-html="parseMarkdown(abstractRes)"></div>
              </a-spin>
            </div>
        </div>
      </div>

</div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ref, onMounted, provide } from "vue";
import { PaperClipOutlined } from '@ant-design/icons-vue';
import { fileAnalysis, createAbstract, } from "@/services/api/abstract";
import type { UploadProps } from 'ant-design-vue';
import { parseMarkdown } from "@/utils/markdownParser";
import { message } from 'ant-design-vue';

const spinning = ref(false);
const router = useRouter()

const handleBack = () => {
  router.back()
}

const countSelect = ref()

const fileList = ref<UploadProps['fileList']>([])
const fileValue = ref<File>();
const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
  fileList.value = [file];
  fileValue.value = file;
  return false;
};

const content = ref('')
const abstractRes =ref('')

watch(fileValue, (newValue, oldValue) => {
  if (newValue){
    console.log(`count changed from ${oldValue} to ${newValue}`)
    getAnalysis()
  }
})

const getAnalysis = async () => {
  const params = {
    file: fileValue.value
  }
  try {
    const res = await fileAnalysis(params)
    console.log(res.data.content_preview,"resresresres");
    content.value = res.data.content_preview
    message.success('文件解析成功！');
  } catch (error) {
    message.error('文件解析失败');
    console.error('文件解析失败:', error);
  }
}


const getAbstract = async () => {
  if(!countSelect.value){
    message.error('请选择提取长度!');
  }
  else if (!fileValue.value){
    message.error('请上传附件!');
  }
  else {
    spinning.value = true
    const params = {
      count: countSelect.value,
      document_content: fileValue.value
    }
    try {
      // 测试
      const res = await createAbstract(params)
      console.log(res,"resresresres");
      abstractRes.value = res.data
      spinning.value = false;
      message.success('摘要提取完成');
    } catch (error) {
      message.error('摘要提取失败');
      console.error('摘要提取失败:', error);
    }
  }
}

// onMounted(async () => {
//     const res = await axios.get('http://************:8000/api/v1/analysis/markify/', {
//       headers: {
//         'Content-Type': 'application/json'
//       }
//     })
//     content.value = res.data
// });


</script>


<style scoped lang="scss">
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  bottom: 35px;
  left: 130px;
  width: 200px;
}
:deep(.ant-upload-list-item-container){
  width: 0;
  height: 0;
}


.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url(@/assets/image/graph/graph_bg.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 90px 40px 50px 40px;
  overflow: auto;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.toolbar-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}


.addbut {
  width: 120px;
  height: 32px;
  border-radius: 150px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 9px 20px 9px 20px;
  gap:10px;
  background: linear-gradient(90deg, rgba(63, 140, 255, 1) 0%, rgba(21, 192, 230, 1) 100%);
}

.box-container {
  width:100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0;
  gap:20px;
}

.box-left {
  flex: 6;
  height: 100%;
  border-radius: 5px;
  background-color: white;
  box-shadow: 0px 2px 19px; 
  padding: 30px;
  display: flex;
  flex-direction: column;
  
  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap:16px;
  }

  .center {
    flex: 1;
    border-radius: 5px;
    margin: 20px 0;
    border: 1.5px solid rgba(230, 239, 255, 1);

  }

  .bottom {
    display: flex;
    justify-content: right;
  }
}

.but{
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.box-right {
  flex: 4;
  height: 100%;
  border-radius: 5px;
  background-color: white;
  box-shadow: 0px 2px 19px; 
  padding: 20px;
  display: flex;
  flex-direction: column;


  .text {
    height: 60px;
    padding-top: 30px;
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 16px;
    color: rgba(51, 51, 51, 1);
    display: flex;
    justify-content: center;
  }

  .content{
    flex: 1;
    border-radius: 5px;
    margin-top: 20px;
    border: 1.5px solid rgba(230, 239, 255, 1);
  }

}
  .textarea {
    overflow: auto;
    max-height: 680px;
    background: transparent;
    padding: 12px;
  }

</style>