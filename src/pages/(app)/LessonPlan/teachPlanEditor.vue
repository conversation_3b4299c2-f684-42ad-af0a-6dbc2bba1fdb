<template>
  <a-spin :spinning="spinning">
      <div class="page">

        <!-- 顶部返回 -->
        <div class="page-header">
          <div class="left">
            <div @click="handleBack" class="back-btn">
              <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg"/>
              <span class="back-text">返回</span>
            </div>

                <span v-if="!editing" @click="startEditing" class="topTitle">{{ title }}</span>
                <input
                  v-else
                  v-model="teachPlanTitle"
                  @blur="saveTitle"
                  ref="inputRef"
                  class="topTitle"
                />

            <div class="date">已保存于 {{ currentTime }}</div>
          </div>
          <div class="right">
            <button class="btn1" @click="handleSave()">保存加关联</button>
          </div>
        </div>

        <div class="page-content">

          <AiEditors :mode="mode" :teachPlanData="teachPlanData" :listData="listData" :title="title" @emitTitle="handleTitle" @updateTeachPlan="updateTeachPlan"/>

        </div>

      </div>
  </a-spin>
</template>

<script lang="ts" setup>
import { AiEditor } from "aieditor"
import "aieditor/dist/style.css"
import { ref, onMounted, onUnmounted, nextTick} from "vue"
import AiEditors from "@/components/lessonPlan/AiEditors.vue";
import { useRoute, useRouter } from 'vue-router'
import { saveTeachPlan, editTeachPlan, editTeachPlanTemp } from '@/services/api/LessonPlan'
import { message } from 'ant-design-vue';

// 获取不同页面跳转路由携带的mode信息（creat=生成教案; edit=编辑教案；temp=编辑教案模板）
const mode = ref(window.history.state.mode as string)

// 获取生成教案跳转路由携带的教案生成信息（desc=描述; template=教案模板）
const teachPlanData = ref({
  desc: window.history.state.desc as string,
  template: window.history.state.template as string,
})

// 获取教案（或模板）管理跳转路由携带的教案（或模板）编辑信息（title=标题; content=内容）
const listData = ref({
  title: window.history.state.title as string,
  content: window.history.state.content as string,
})

// 获取教案（或模板）管理跳转路由携带的教案（或模板）编辑信息（保存时需要的id）
const id =ref(window.history.state.id)

const spinning = ref(false)

const router = useRouter()
const handleBack = () => {
  router.back()
}

const teachPlanItems = ref('');
const updateTeachPlan = (teachPlan: any) => {
  // console.log("收到子组件传来的教案:", teachPlan)
  teachPlanItems.value = teachPlan
}

const title = ref('这是一个可编辑标题')
const teachPlanTitle = ref('')
const editing = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)

onMounted(() => {
  // 页面加载时，如果是编辑模式，则从路由状态中获取标题
    if (mode.value != "create") {
      title.value = listData.value.title || '这是一个可编辑标题'
    } 
})


const handleTitle = (params: string) => {
  // console.log("收到子组件传来的标题:", teachPlan)
  title.value = params
}

function startEditing() {
  teachPlanTitle.value = title.value
  editing.value = true
  // 等 DOM 渲染完成后聚焦
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function saveTitle() {
  title.value = teachPlanTitle.value.trim() || title.value
  editing.value = false
}


const currentTime = ref('')

// 格式化当前时间为 YYYY-MM-DD HH:mm:ss
function formatTime(date: Date): string {
  const Y = date.getFullYear()
  const M = String(date.getMonth() + 1).padStart(2, '0')
  const D = String(date.getDate()).padStart(2, '0')
  const h = String(date.getHours()).padStart(2, '0')
  const m = String(date.getMinutes()).padStart(2, '0')
  const s = String(date.getSeconds()).padStart(2, '0')
  return `${Y}-${M}-${D} ${h}:${m}:${s}`
}

const handleSave = () => {
  if (mode.value === "create") {
    savePlan()
  } else if (mode.value === "edit") {
    saveEditPlan()
  } else {
    saveEditPlanTemp()
  }
}


const savePlan = async() => {

  const content = `# ${title.value}\n${teachPlanItems.value}`

  const params = {
      title: title.value,
      content: content,
  }
  try {
    spinning.value = true
    const res = await saveTeachPlan(params)
    console.log(res);
    spinning.value = false //加载
    message.success('保存成功！')//保存成功提示
    currentTime.value = formatTime(new Date())
  } catch (error) {
    message.error('保存失败')
  }
}

const saveEditPlan = async() => {
  
  const content = `# ${title.value}\n${teachPlanItems.value}`

  const params = {
      title: title.value,
      content: content,
  }
  try {
    spinning.value = true
    const res = await editTeachPlan(id.value, params)
    console.log(res);
    spinning.value = false //加载
    message.success('保存成功！')//保存成功提示
    currentTime.value = formatTime(new Date())
  } catch (error) {
    message.error('保存失败')
  }
}

const saveEditPlanTemp = async() => {
  
  const content = `# ${title.value}\n${teachPlanItems.value}`

  const params = {
      title: title.value,
      content: content,
  }
  try {
    spinning.value = true
    const res = await editTeachPlanTemp(id.value, params)
    console.log(res);
    spinning.value = false //加载
    message.success('保存成功！')//保存成功提示
    currentTime.value = formatTime(new Date())
  } catch (error) {
    message.error('保存失败')
  }
}

</script>

<style scoped lang="scss">
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; 

  .page-header{
    position: relative; /* 创建层叠上下文 */
    z-index: 1; /* 确保阴影在上层 */
    min-width: 800px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
    background-color: #ffffff;
    box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
    padding: 0 20px;

    .topTitle{
      color: #333;
      // width: 300px;
      font-size: 20px;
      font-weight: 700;
      white-space: nowrap;      /* 不换行 */
      overflow-x: auto;         /* 超出时显示横向滚动条 */  
    }

    .left {
      display: flex;
      align-items: center;
      gap:20px
    }
    .right {
      display: flex;
      align-items: center;
    }

    .back-btn {
      cursor: pointer;
      // margin-right: 20px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .date {
      color: gray;
    }

    .btn1{
      width: 95px;
      height: 32px;
      background: white;
      padding: 10px;
      border-radius: 130px;
      border: 1px solid rgba(229, 229, 229, 1);
      margin-left: 1vw;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 14px;
      color: rgba(102, 102, 102, 1);
    }

    .btn2{
      width: 10vw;
      height: 5vh;
      background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
      color: white;
      font-weight: bold;
      padding: 0.5vh 0.8vw;
      border-radius: 2vw;
      border: none;
      margin-left: 1vw;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }

}


.page-content {
  flex:1;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: cover;
  box-sizing: border-box;
  display: flex;
  justify-content:space-between;
  overflow: hidden;
}

</style>