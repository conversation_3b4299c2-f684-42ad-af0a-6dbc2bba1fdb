<template>
  <a-spin :spinning="spinning">
  <div class="page">
    <!-- 顶部返回 -->
    <div class="page-header">
      <div class="left">
        <div @click="handleBack" class="back-btn">
          <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg" />
          <span class="back-text">返回</span>
        </div>
      </div>
      <div class="center">教案模板生成</div>
      <div class="right"></div>
    </div>

    <div class="page-content">
      <!-- 主要内容区域 -->
      <div class="main-content">

        <div class="chat-window">
          <div class="avatar">
            <img src="@/assets/image/avatar.svg" alt="机器人图标">
          </div>

          <img v-if="loading" src="@/assets/image/3dots.svg" style="width: 36px;height: 36px;">

          <div class="bubble" v-else>
            <div class="subtitle">根据课程-授课方向返回教案模板</div>

              <!-- <pre ref="outlineRef" v-if="outlineCreating">{{ outline }}</pre>
              <div class="chat-content" v-else>
                <OutlineTimeline :items="timelineItems"/>
              </div> -->

            <!-- <div class="chat-content" >
              {{ timelineItems }}
              <OutlineTimeline :items="timelineItems" />
            </div> -->
            <div class="chat-content" v-if="isCreating" v-html="parseMarkdown(timelineItems)"></div>
            <div class="chat-content" v-else>
              <Outline :content="content"/>
            </div>
          </div>

        </div>

        <!-- 保存按钮 -->
        <button class="save-btn" @click="saveTemp()">保存</button>

        <!-- 底部输入区域 -->
        <div class="input-wrapper">
          <input 
            type="text" 
            placeholder="请输入教案模板主题，如：物理"
            v-model="keyword"
            class="input-field"
            @enter="createTeachPlanTemp()"
          >

          <div class="input-actions">
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                课程章节
                <img style="width: 12px;height: 12px;" src="@/assets/image/ArrowDown.svg" />
              </span>
              <template #dropdown>
                <chapter-selector :tree-data="treeData" @update:checked="handleCheckedUpdate"
                  ref="chapterSelectorRef" />
              </template>
            </el-dropdown>
            <span class="attach">
              <a-upload style="display: flex;align-items: center;" :file-list="fileList" :maxCount="1" accept=".doc,.docx"  @remove="handleRemove" :before-upload="beforeUpload">
                  <img style="width: 16px;height: 16px;" src="@/assets/image/clip.svg" />
              </a-upload>
            </span>
            <span class="send" @click="createTeachPlanTemp()">
              <img style="width: 18px;height: 16px;" src="@/assets/image/plane.svg"/>
            </span>
          </div>
        </div>

      </div>

      
    </div>
  </div>
  </a-spin>
</template>

<script lang="ts" setup>
import { parseMarkdown } from "@/utils/markdownParser";
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import ChapterSelector from '@/components/lessonPlan/ChapterSelector.vue'
import { saveTeachPlanTemp, getTeachPlanTemp } from '@/services/api/LessonPlan'
import type { UploadProps } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import  Outline  from "@/components/lessonPlan/OutLine.vue";

const router = useRouter()
const handleBack = () => {
  router.back()
}

const spinning = ref(false);
const keyword = ref('')
const loading = ref(false)
const isCreating = ref(false)
const content = ref('')

const fileList = ref<UploadProps['fileList']>([])
const fileValue = ref<File>();
const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
  fileList.value = [file];
  fileValue.value = file;
  return false;
};

const timelineItems = ref('');

const createTeachPlanTemp = async () => {
  isCreating.value = true
  loading.value = true

  timelineItems.value = ''
  if (keyword.value === '') {
    message.error('请输入模板主题！');
  }

  const params = {
    theme:keyword.value,
    document:fileValue.value
  }

  const res = await getTeachPlanTemp(params)

  if (!res.body) {
    message.error('响应体为空');
    return;
  }
  if (!res.ok) {
    message.error(`HTTP请求错误! 状态码: ${res.status}`);
    return;
  }

  const reader: ReadableStreamDefaultReader = res.body.getReader()
  const decoder = new TextDecoder('utf-8')

  const readStream = () => {
    reader.read().then(({ done, value }) => {
      loading.value = true
      
      if (done) {
        isCreating.value = false
        content.value = timelineItems.value
        return
      }

      const chunk = decoder.decode(value, { stream: true })
      timelineItems.value += chunk
      const chatContent = document.querySelector('.chat-content');

      if (chatContent) {
        chatContent.scrollTop = chatContent.scrollHeight;
      }

      readStream()
    })
  }
  readStream()    
}


const saveTemp = async() => {
  const match = timelineItems.value.match(/^# (.+)$/m);
  const title = match ? match[1] : '无标题';

  const params = {
      title: title,
      content: content.value,
  }
  try {
    spinning.value = true
    const res = await saveTeachPlanTemp(params)
    spinning.value = false
    message.success('保存成功！')
  } catch (error) {
    message.error('获取PPT列表失败')
  }
}


const treeData = [
  {
    id: 1,
    label: '课程XXX',
  },
  {
    id: 2,
    label: '课程XXX',
  }
]

const checkedKeys = ref<Array<number | string>>([])

const handleCheckedUpdate = (val: Array<number | string>) => {
  checkedKeys.value = val
}


</script>

<style scoped lang="scss">
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  bottom: 5vw;
  right: 5vw;
  width: 200px;
}
:deep(.ant-upload-list-item-container){
  width: 0;
  height: 0;
}

.page {
  width: 100%;
  height: 100vh;
  min-width: 800px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    position: relative; /* 创建层叠上下文 */
    z-index: 1; /* 确保阴影在上层 */
    min-width: 800px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    box-shadow: 0px 2px 10px  rgba(67, 143, 254, 0.1);
    padding: 0 20px;

    .left,
    .right {
      display: flex;
      align-items: center;
    }

    .center {
      flex: 1;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
    }

    .back-btn {
      cursor: pointer;
      margin-right: 20px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .page-content {
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: cover;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px);// 添加这个

    .main-content {
      flex: 1;/* 关键属性 - 填充剩余空间 */
      min-height: 0; // 添加这个
      background-color: transparent;
      border-radius: 8px;
      padding: 30px 0;
      width: 45%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .chat-window {
        flex: 1;
        min-height: 0; // 添加这个
        display: flex;
        gap: 10px;

        .avatar {
          flex-shrink: 0;
          width: 36px;
          height: 36px;
        }

        .bubble {
          flex: 1;
          min-height: 0; // 添加这个
          overflow: auto;
          box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);
          padding: 20px;
          border-radius: 5px;
          background-color: #ffffff;
          color: #000;
          white-space: pre-wrap;
          word-break: break-word;
          line-height: 1.4;
          font-size: 16px;
          display: flex;
          flex-direction: column;

          .subtitle {
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(229, 229, 229, 1);
          }

          .chat-content {
            flex:1;
            overflow: auto;
            scrollbar-width: none;
            margin-top: 20px;
          }
        }
      }

      .save-btn {
        width: 100px;
        background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
        color: white;
        border: none;
        padding: 8px 10px;
        border-radius: 4px;
        cursor: pointer;
        margin: 20px 46px;
      }

      .input-wrapper {
        display: flex;
        background-color: #ffffff;
        padding: 0.8vw;
        border-radius: 0.5vw;
        box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);

        .input-field {
          flex: 1;
          border: none;
          outline: none;
          padding: 0 1vw;
          font-size: 1vw;
          border-radius: 0.5vw;
          background-color: #ffffff;
        }

        .input-actions {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .el-dropdown-link {
          width: 6vw;
          height: 4vh;
          font-size: 0.8vw;
          font-weight: 500;
          gap: 0.2vw;
          cursor: pointer;
          background-color: white;
          user-select: none;
          border: 0.1vw solid #dcdfe6;
          padding: 0.5vh 0.8vw;
          border-radius: 94px;
          display: inline-flex;
          align-items: center;
        }

        .attach,
        .send {
          cursor: pointer;
        }

        /* .attach {
          display: inline-flex;
          align-items: center;
        } */

        .send {
          width: 50px;
          height: 30px;
          background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
          color: white;
          padding: 7px 15px;
          border-radius: 148px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
</style>
