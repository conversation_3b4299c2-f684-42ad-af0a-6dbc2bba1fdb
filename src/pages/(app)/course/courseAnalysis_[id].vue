<template>
  <div class="page">

      <div @click="handleBack" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div>
      <h1 class="text">计算机网络-学情分析-学生xxx</h1>

      <div class="summary">
        <div class="summary_header">
          <div class="summary_title">
            <img src="@/assets/image/course/analysis.png" style="width: 17px; height: 17px; margin-right: 10px;"/>
            学生学情分析报告
          </div>
          <div class="summary_but">
            <a-button type="primary" class="btn">导出pdf</a-button>
            <a-button type="primary" class="btn">导出word</a-button>
          </div>
        </div>
        <div style="flex:1;  display: flex; flex-direction: row;">
            <div style="flex:1">
                <div class="summary_content">
                    文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充文本填充
                </div>
            </div>
            <div style="position: relative; width: 366px;">
              <img src="@/assets/image/course/ai.png" style="position: absolute;z-index:1;height: 366px; width: 366px;bottom:-70px"/>
            </div>
        </div>
      </div>
      
  </div>
</template>

<script lang="ts" setup>

const router = useRouter()
const handleBack = () => {
  //需要动态配置课程id
  router.back()
}


</script>

<style scoped lang="scss">

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;

  .back-btn {
    cursor: pointer;
    width: 60px;
    margin-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
  }

  .text {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    margin: 20px 0 20px;
  }

  .summary{
    min-height: 800px;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4.79px;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .summary_header{
      display: flex;
      align-items: center;
      justify-content: space-between; 
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(229, 229, 229, 1);
      .summary_title{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 24px;
      }
      .summary_but{
        display: flex; 
        justify-content: 
        space-between; gap:15px
      }
    }

    .summary_content {
        padding-top: 40px;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0px;
        line-height: 20px;
        color: rgba(102, 102, 102, 1);
        text-align: left;
        overflow: auto;
    }
    .btn{
        background: rgba(63, 140, 255, 1); 
        width: 71px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
  }

}

</style>
