<template>
    <div class="course">
        <div class="title">我的课程</div>
        <div class="selectclass">
            <div class="selectclass-left">
                <el-select v-model="valueSelect" placeholder="课程学期" style="width: 186px;min-height: 30px;"
                    suffixIcon="CaretBottom" clearable @change="onSearch" >
                    <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
                <!-- <a-select
                    ref="select"
                    v-model:value="valueSelect"
                    style="width: 186px"
                    placeholder="选择学期"
                    @change="onSearch"
                >
                    <a-select-option  v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
                        {{item.name}}
                    </a-select-option>
                </a-select> -->

                <!-- <a-select v-model:value="valueSelect" show-search placeholder="请选择学期" :options="options"
                    style="width:186px;height: 30px;" allowClear>
                    <template #suffixIcon><CaretUpOutlined  /></template>
                </a-select> -->
                <el-input v-model="searchValue" style="width: 302px;min-height: 30px;margin: 0 16px 0 10px;"
                    placeholder="请输入" clearable @clear="getCourseList(valueSelect)" @change="getCourseList(valueSelect)" >
                    <template #suffix>
                        <el-icon @click="getCourseList(valueSelect)">
                        <Search />
                        </el-icon>
                    </template>
                </el-input>
                <div style="font-size: 14px;">共有<span style="color: rgba(63, 140, 255, 1);">{{ courseList.length }}</span>条结果</div>
            </div>
            <div style="font-size: 14px;width: 82px;height: 32px;">
                <el-button @click="createCourse" round>创建课程</el-button>
            </div>
        </div>
        <div class="course-list">
            <div class="course-item" v-for="(item, index) in courseList" :key="index" @click="clickCourse(item)">
                <div style="height: 181px">
                    <img :src="item.image" style="width: 100%; height: 100%; object-fit:cover;margin: auto;"/>
                </div>
                <div style="height: 89px;">
                    <div class="course-title">{{ item.title }}</div>
                    <div class="course-info">
                        <div style="color: #666;">
                            <!-- {{ item.semester }} -->
                        </div>
                        <el-popover v-model:visible="item.visible"  trigger="hover" placement="bottom-end" :width="87"
                            :popper-style="{ minWidth: '87px', padding: '0', width: 'auto' }">
                            <div class="popover">
                                <div class="flex" style="">
                                    <el-icon>
                                        <Edit />
                                    </el-icon>
                                    <div style="margin-left: 6px;" @click.stop="clickPopover(item)">编辑</div>
                                </div>
                                <div class="flex">
                                    <el-icon>
                                        <CircleClose />
                                    </el-icon>
                                    <div style="margin-left: 6px;" @click.stop="deteleCourse(item)">删除</div>
                                </div>
                            </div>
                            <template #reference>
                                <el-icon class="popover-icon" style="cursor: pointer;">
                                    <MoreFilled />
                                </el-icon>
                            </template>
                        </el-popover>

                    </div>
                </div>
            </div>
        </div>

        <a-modal v-model:open="open" :title="modelTitle" :footer="null" width="700px">
            <div style="margin: 50px 0 0 0;">
                <a-form :model="course" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
                    autocomplete="off">
                    <a-form-item label="课程名称" name="title" v-bind="validateInfos.title">
                        <a-input v-model:value="course.title" placeholder="请输入课程名称" />
                    </a-form-item>
                    <!-- <a-form-item label="课程学期" v-bind="validateInfos.title">
                        <a-select v-model:value="course.semester" show-search placeholder="请选择课程学期"
                            :options="options"></a-select>
                    </a-form-item>
                    <a-form-item label="知识库">
                        <a-select v-model:value="course.konwelage" show-search placeholder="请选择绑定知识库"
                            :options="konwelageOptions"></a-select>
                    </a-form-item> -->
                    <a-form-item label="课程封面" v-bind="validateInfos.image">
                        <uploadAnt :urlList="imageCourse" @beforeUploadgetfile="getfile" @deleteFile="delFile" />
                    </a-form-item>
                    <div style="display: flex;justify-content: flex-end">
                        <a-form-item>
                        <a-space>
                                <a-button @click=" open = false">取消</a-button>
                                <a-button type="primary" html-type="submit" @click="onSubmit" style=""
                                :loading="loadingSubmit">确定</a-button>
                            </a-space>
                    </a-form-item>
                    </div>
                </a-form>
            </div>
        </a-modal>
    </div>
</template>
<script lang="ts" setup>
import { useCourseStore } from '@/stores/course'
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import uploadAnt from '@/components/ui/uploadant.vue'
import { courseAdd, courseListapi, courseDelete, courseEdit, semesterList } from '@/services/api/course'
import { Search } from '@element-plus/icons-vue'
import { CaretUpOutlined } from '@ant-design/icons-vue'
import { Form } from 'ant-design-vue';
import { message } from 'ant-design-vue';
const useForm = Form.useForm
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const modelTitle = ref('创建课程') //弹窗标题
const imageCourse = ref('') //课程图片

const valueSelect = ref()
const searchValue = ref('')

//读取pinia里买呢存储的课程id以及用户id
const courseStore = useCourseStore()
const { semester_id, user_id } = toRefs(courseStore)

interface optionsType {
    name: string;
    id: number;
}
const options = ref<optionsType[]>([]) //学期列表数据

import imgUrl0 from '@/assets/image/img/4.png'
import imgUrl1 from '@/assets/image/img/1.png'
import imgUrl2 from '@/assets/image/img/2.png'
import imgUrl3 from '@/assets/image/img/3.png'
interface Course {
    title: string;
    description: string;
    semester: number;
    konwelage: string;
    image: string;
    teacher: number;
    role: string;
    visible: boolean;
}
const courseList = ref<Course[]>([])
//课程列表
function getCourseList(semester:number) {
    
    const params = {
        semester_id: semester,//学期id
        // user_id:user_id.value,
        title: searchValue.value
    }
    courseListapi(params).then(res => {
        console.log(res.data, '课程列表')
        courseList.value = res.data.results.map((item:any) => {
            return {
                ...item,
                visible: false // 控制是否显示 popover
            }
        })
    })
}

function onSearch() {
    getCourseList(valueSelect.value)
}


//创建课程弹窗
const open = ref<boolean>(false);
watch(open, (newValue) => {
    if (!newValue) {
        resetFields()
        imageCourse.value = ''
    }
})
// interface FormState {
//     title: string;
//     semester: Array<string>;
//     konwelage:  string;
//     image: File;
//     teacher: Array<number>;
//     role: Array<string>;
// }
const course = reactive({
    title: '',
    description: '',//描述
    semester: '',
    konwelage: '',
    image: '',
    teacher: 2,
    role: 'teacher'
});
const courseRules = reactive({
    title: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
    // semester: [{ required: true, message: '请选择学期', trigger: 'change' }],
    image: [{ required: true, message: '请上传课程封面', trigger: 'change' }],
})

//获取封面文件流
const getfile = (file: any) => {
    course.image = file
}
const delFile = (file: any) => {
    course.image = ''
}

//创建课程弹窗
function createCourse() {
    modelTitle.value = '创建课程'
    
    imageCourse.value = ''
    resetFields()
    open.value = true
}

const loadingSubmit = ref(false)
const { resetFields, validate, validateInfos } = useForm(course, courseRules);//验证课程表单提交
const onSubmit = () => {
    validate().then(() => {
        onSubmitSuccess()
    }).catch(err => {
        console.log('error', err);
    });
};
const onSubmitSuccess = () => {
    loadingSubmit.value = true
    const formData = new FormData();
    formData.append('title', course.title);
    formData.append('semester', JSON.stringify(semester_id.value)); // 转为字符串
    formData.append('konwelage', course.konwelage);
    formData.append('image', course.image);
    formData.append('teacher', JSON.stringify(course.teacher));
    formData.append('role', course.role);

    if(modelTitle.value === '编辑课程'){
        formData.append('id', courseEditId.value);
        if(typeof course.image == 'string'){
            formData.delete('image');
        }
        courseEdit(formData).then(res => {
            loadingSubmit.value = false
            open.value = false
            course.image = ''
            message.success('编辑成功')
            getCourseList(valueSelect.value)
            resetFields();
        }).catch(err => {
            loadingSubmit.value = false
            message.error('编辑失败')
        })
        return
    }

    courseAdd(formData).then(res => {
        loadingSubmit.value = false
        open.value = false
        course.image = ''
        message.success('创建成功')
        getCourseList(valueSelect.value)
        resetFields();
    }).catch(err => {
        loadingSubmit.value = false
        message.error('创建失败')
    })
};

const courseEditId = ref('')
function clickPopover(item: any) {
    console.log(item, '点击了')
    item.visible = false // 关闭 popover
    open.value = true
    modelTitle.value = '编辑课程'
    course.title = item.title
    course.image = item.image
    imageCourse.value = item.image //课程图片封面
    courseEditId.value = item.id
}
//删除课程
async function deteleCourse(item:any){
    const confirmed = await showDeleteConfirm('确定删除本记录吗?删除后无法恢复!');
    console.log(item.id, 'item');
    if (confirmed) {
        courseDelete(item.id).then(res => {
            message.success('删除成功')
            getCourseList(valueSelect.value)
        })
    }
}
function clickCourse(item: any) {
    courseStore.setCourseId(item.id)
    courseStore.setCourseDetails(item) //存储课程详情
    router.push({
        path: `course/${item.id}`,
    })
}


async function getsemesterList() {
   const data = await semesterList()
   options.value = data.data
   
//    console.log(data.data.slice(-1), '学期列表')
   semester_id.value = data.data.slice(-1)[0].id
    valueSelect.value = data.data.slice(-1)[0].id
    course.semester = data.data.slice(-1)[0].id
    // console.log(data.data.slice(-1)[0].id, '学期列表')
   //  获取课程列表
    getCourseList(valueSelect.value)
}


// 获取学期列表
getsemesterList()
</script>
<style scoped lang="scss">
:deep(.el-select__wrapper) {
    border-radius: 50px;
}
:deep(.ant-modal){
    height: 539px !important;
}

:deep(.el-input__wrapper) {
    border-radius: 50px !important;
}
// :deep(.ant-form-item){
//     display: flex;
//     justify-content: space-between;
// }

:deep(.ant-select-selector) {
    border-radius: 20px;
}

:deep(.ant-select-lg) {
    border-radius: 40px !important;
}

:deep(.el-button) {
    background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
    color: #fff;
    font-size: 14px !important;
}

:deep(.el-input__suffix-inner) {
    cursor: pointer;
}

:deep(.el-popover .el-popper) {
    min-width: 100px !important;
    width: 100px !important;
}

.course {
    background-image: url('@/assets/image/img/bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    // background-size: 100% 100%;
    min-height: 100vh;
    min-width: 900px;
    width: 100%;
    padding: 0% 0 0 40px;

    .title {
        padding: 90px 0 0 0%;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 12px;
        // height: 12px;
    }

    .selectclass {
        margin: 20px 40px 20px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .selectclass-left {
            display: flex;
            align-items: center;
        }
    }

    .course-list {
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-between;
        // padding: 0 0 0 20px;

    }

    .course-item {
        width: 335px;
        height: 270px;
        border-radius: 6.9px;
        // background-image: url('../../assets/image/img/curriculum0.png');
        // background-repeat: no-repeat;
        // background-size: 100% 67%;
        box-shadow: 0px 2px 10px rgba(67, 143, 254, 0.1);
        background-color: #fff;
        position: relative;
        margin: 0 20px 20px 0px;
        cursor: pointer;

        &:hover {
            transform: translateY(-5px); // 悬浮上移
            box-shadow: 0px 8px 24px rgba(29, 79, 153, 0.2); // 加深阴影
        }

        .course-title {
            font-size: 16px;
            font-weight: 700;
            margin: 20px 20px 0 20px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            overflow: hidden;
            line-height: 16px;
            height: 16px;
        }

        .course-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin:20px;
            font-size: 12px;
            line-height: 12px;
            height: 12px;
        }
    }
}

.popover {
    display: flex;
    justify-content: center;
    padding: 10px 0;
    flex-direction: column;
    align-items: center;

    .flex {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        width: 67px;
        height: 34px;
    }

    .flex:hover {
        border-radius: 3px;
        background: rgba(63, 140, 255, 0.1);
        // width: 67px;
        height: 34px;
        text-align: center;
        color: rgba(63, 140, 255, 1);
        font-weight: 600;
    }
}

.popover-icon {
    color: #666; // 默认颜色继承父级

    &:hover {
        color: rgb(63, 140, 255); // 悬停时的颜色background: ;
    }
}
</style>