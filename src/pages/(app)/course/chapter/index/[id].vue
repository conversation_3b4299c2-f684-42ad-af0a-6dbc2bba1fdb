<template>
    <div class="chapter">
        <back :url="`/course/${route.params.id}`"></back>
        <div class="text-[24px] leading-[12px] my-[20px] font-bold">计算机网络-章节</div>
        <div class="gap-[10px] flex">
            <!-- <el-select v-model="valueSelect" placeholder="班级选择" style="width: 100px;" suffixIcon="CaretBottom"
                clearable>
                <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
            </el-select> -->

            <div class="flex-1">
                <el-input v-model="searchValue" style="width: 302px;height: 30px;"  placeholder="请输入" clearable @clear="getchapterList">
                    <template #suffix>
                        <el-icon @click="getchapterList">
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
            </div>
            <a-button class="w-[82px] h-[30px]" @click="openChapter = true" v-if="chapter.length"
                style="background: linear-gradient(90deg, #3F8CFF 0%, #15C0E6 100%);color: #fff;border-radius: 150px;">
                新增章
            </a-button>
        </div>

        <div class="bg-white mt-[10px] rounded-[0.4px] pt-[20px] pb-[20px] pl-[20px] pr-[32px]" v-for="item in chapter"
            v-if="chapter.length">
            <div class=" flex justify-between pr-[20px]" :class="{ 'mb-[20px]': item.open }">
                <div class="flex items-center gap-[10px]">
                    <div class="w-[14px] cursor-pointer relative" @click="item.open = !item.open">
                        <CaretDownOutlined v-if="item.open" />
                        <CaretRightOutlined v-else />
                        <div v-if="item.open" class="absolute left-1/2 top-full  w-px bg-[#E5E5E5] -translate-x-1/2"
                            :style="{ height: `calc(66px * ${item.children.length})` }"></div>
                    </div>
                    <img src="@/assets/image/course/analysis.png" class="w-[17px] h-[17px]">
                    <div class="font-bold text-[16px] leading-[16px]">{{ item.title }}</div>
                </div>
                <div class="flex items-center gap-[10px]">
                    <div class="text-[#666666] text-[14px]">章节进度：</div>
                    <div class="w-[138px] mr-[60px]">
                        <a-progress :percent="item.progress" style="margin: 0;" />
                    </div>
                    <div class="flex items-center gap-[20px]">
                        <a-tooltip>
                            <template #title>编辑</template>
                            <img src="@/assets/image/img/edittable.png" class="w-[14px] h-[14px] cursor-pointer"
                                @click="editChapter(item,item.id)" />
                        </a-tooltip>
                        <a-tooltip>
                            <template #title>查看</template>
                            <img src="@/assets/image/img/viewIcon.png" class="w-[14px] h-[14px] cursor-pointer"
                                @click="viewChapter(item,item.id)" />
                        </a-tooltip>
                        <a-tooltip>
                            <template #title>删除</template>
                            <img src="@/assets/image/img/blueDel.png" class="w-[14px] h-[14px] cursor-pointer"
                                @click="delChapter(item)" />
                        </a-tooltip>
                    </div>
                </div>
            </div>



            <div v-if="item.open" class=" bg-[rgba(204,204,204,0.07)] p-[20px] ml-[24px] mt-[10px] 
                border border-[#F2F2F2] rounded-[5px] relative" v-for="i in item.children">

                <div
                    class="absolute left-[-18px] top-1/2 h-px w-[18px] bg-[#E5E5E5] before:absolute before:content-[''] before:left-0 before:top-1/2 before:w-full before:h-px before:bg-[#E5E5E5]">
                </div>

                <div class="flex items-center justify-between h-[14px]">
                    <div class="flex items-center gap-[10px]">
                        <div class="w-[10px] h-[10px] bg-[#3F8CFF] rounded-[50%]"></div>
                        <div class="font-bold text-[14px] leading-[14px]">{{ i.title }}</div>
                    </div>
                    <div class="flex gap-[10px]">
                        <div class="w-[138px] mr-[60px]">
                            <a-progress :percent="i.progress" style="margin: 0;" />
                        </div>
                        <div class="flex items-center gap-[20px]">
                            <img src="@/assets/image/img/edittable.png" class="w-[14px] h-[14px]" @click="editChapter(item,i.id)"/>
                            <img src="@/assets/image/img/viewIcon.png" class="w-[14px] h-[14px]"  @click="viewChapter(item,i.id)"/>
                            <a-tooltip>
                                <template #title>删除</template>
                                <img src="@/assets/image/img/blueDel.png" class="w-[14px] h-[14px] cursor-pointer"
                                    @click="delChapter(item)" />
                            </a-tooltip>
                        </div>
                    </div>
                </div>

            </div>
        </div>



        <div class="flex items-center justify-center flex-col h-[calc(100vh-156px)]" v-else>
            <img src="@/assets/image/zanwu/zanwuIcon.png" class="w-[309px] h-[221px]" />
            <div class="text-[#666666] text-[14px]">暂无章节目录</div>
            <a-button class="w-[82px] h-[32px] mt-[27px]" @click="openChapter = true"
                style="background: linear-gradient(90deg, #3F8CFF 0%, #15C0E6 100%);color: #fff;border-radius: 150px;">
                新增章
            </a-button>
        </div>

        <a-modal v-model:open="openChapter" width="700px" :footer="null" title="新增章节">
            <a-form layout="vertical">
                <a-form-item label="章节名称" v-bind="validateInfos.title">
                    <QuestionEditor :questionValue="chapterDetails.title" @valueEditorChange="getChapterTitle"></QuestionEditor>
                    <!-- <a-input v-model:value="chapterDetails.title"></a-input> -->
                </a-form-item>
                <a-form-item label="章节描述">
                    <QuestionEditor :questionValue="chapterDetails.content" @valueEditorChange="getChapterContent"></QuestionEditor>
                </a-form-item>
                <div class="flex justify-end">
                    <a-form-item>
                        <a-space>
                            <a-button @click=" openChapter = false">取消</a-button>
                            <a-button type="primary" html-type="submit" @click.prevent="onSubmit" style=""
                                :loading="loadingSubmit">确定</a-button>
                        </a-space>
                    </a-form-item>
                </div>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang="ts" setup>
import { chapterAdd, chapterDelete } from '@/services/api/course'
import { CaretUpOutlined, CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import { chapterList } from '@/services/api/course'
import back from '@/components/ui/back.vue';
import { useRouter, useRoute } from 'vue-router'
import { Form } from 'ant-design-vue';
const useForm = Form.useForm;
const router = useRouter()
const route = useRoute() //获取路由参数
import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()


const valueSelect = ref('');
const searchValue = ref('');
const options = [
    {
        id: '1',
        name: '2021级软件工程1班'
    }
]

interface Chapter {
    id: number,
    title: string,
    progress: number,
    open: boolean,
    children: {
        id: number,
        title: string,
        content: string,
        progress: number,
    }[]
}
const chapter = ref<Chapter[]>([])
const i = [1, 2, 3, 4, 5]


const openChapter = ref(false)
const loadingSubmit = ref(false)
interface ChapterDetail {
    course: number | string | string[],
    title: string,
    content: string,
}
const chapterDetails = reactive<ChapterDetail>({
    course: route.params.id,
    title: '',
    content: '',
})
const { resetFields, validate, validateInfos } = useForm(chapterDetails, reactive({
    title: [
        {
            required: true,
            message: '请输入章节名称!',
        },
    ]
}),);//验证课程表单提交
const onSubmit = () => {
    validate().then(() => {
        loadingSubmit.value = true
        chapterAdded()
    }).catch((err: any) => {
        loadingSubmit.value = false
    });
};

//新增zhangji
function chapterAdded() {
    const params = {
        ...chapterDetails,
    }
    chapterAdd(params).then((res: any) => {
        if (res.code === 200) {
            openChapter.value = false
            loadingSubmit.value = false
            getchapterList()
        }
    })
}

const getChapterTitle = (value: any) => {
    chapterDetails.title = value.valueEditor
}
const getChapterContent = (value: any) => {
    chapterDetails.content = value.valueEditor
}
// 获取章节列表
function getchapterList() {
    const params = {
        course_id: route.params.id,
        title:searchValue.value
    }
    // return
    chapterList(params).then((res: any) => {
        console.log(res)
        if (res.code == 200) {
            chapter.value = res.data
        }
    })
}

function editChapter(item: any,i: any) {
    console.log(item, i,'item');
    
    courseStore.setChapterDetails(item)
    setTimeout(() => {
        router.push({
            path: '/chapter/edit',
            query: {
                id: i,
                chapterId: item.id,
                courseId: route.params.id
            }
        })
    }, 100)
}
function viewChapter(item: any,i: any) {
    console.log(item, i,'item');
    // return
    router.push({
        path: '/chapter/view',
        query: {
            id: i,
            chapterId: item.id,
            courseId: route.params.id
        }
    })
}
async function delChapter(item: any) {
    const confirmed = await showDeleteConfirm('确定删除本章节吗？章节关联的内容将自动解除绑定！');
    if (confirmed) {
        chapterDelete(item.id).then((res: any) => {
            if (res.code == 200) {
                getchapterList()
            }
        })
    }
}

getchapterList()
</script>
<style scoped lang="scss">
:deep(.el-select__wrapper) {
    border-radius: 50px;
    min-height: 30px !important;
}

:deep(.el-input__wrapper) {
    border-radius: 50px !important;
}

.chapter {
    min-height: 100vh;
    background: #F0F9FF;
    padding: 0 40px;
    background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
    background-repeat: no-repeat;
    background-size: 100% 50%;
    background-position: center bottom;
}
</style>
