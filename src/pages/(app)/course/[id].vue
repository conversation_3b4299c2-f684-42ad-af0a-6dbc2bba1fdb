<template>
    <div class="details">
        <back :url="'/course'" />
        <div class="title">
            计算机网络
        </div>
        <div class="teacher flex" >
            <el-icon style="width: 12px;height: 12px;margin-right: 3px;">
                <User />
            </el-icon>
            <div class="" style="margin-right: 20px;">授课老师：xxx</div>
            <el-icon style="width: 12px;height: 12px;margin-right: 3px;">
                <Calendar />
            </el-icon>
            <div style="margin-right: 34px;">
                教学学期：(2024-2025第一学期)
            </div>
            <div class="flex" style="color: #3F8CFF;cursor: pointer;">
                <div>管理教学团队</div>
                <el-icon style="width: 12px;height: 12px;margin-left: 3px;">
                    <Right />
                </el-icon>
            </div>
        </div>

        <div class="konwledge">
            <div class="titlek">知识库</div>
            <div class="flex">
                <div class="num2" style="margin-right: 20px;">
                    <div class="numTite">资源数量</div>
                    <div class="numBer">1</div>
                </div>
                <div class="num1">
                    <div class="numTite">知识点数量</div>
                    <div class="numBer">1</div>
                </div>
            </div>
        </div>

        <div class="konwledge" style="margin-top: 20px;">
            <div class="titlek">资料库</div>
            <div class="flex" style="justify-content: space-between;">
                <div class="homewoke1 homewokeCom" @click="interTeachPlanManage()">
                    <div class="numTite">教案数量</div>
                    <div class="numBer">1</div>
                </div>
                <div class="homewoke2 homewokeCom" @click="interPptManage()">
                    <div class="numTite">PPT数量</div>
                    <div class="numBer">1</div>
                </div>
                <div class="homewoke3 homewokeCom" @click="() => router.push('/quetionList')">
                    <div class="numTite">题库数量</div>
                    <div class="numBer">1</div>
                </div>
            </div>
        </div>

        <div class="items">
            <div class="item" v-for="(item, index) in items" :key="index" @click="handleClick(item)">
                 <img :src="getImageUrl(index)" />
                <div class="titleItem">{{ item.title }}</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import back from '@/components/ui/back.vue';
import { useRouter, useRoute } from 'vue-router'

import {env} from '@/../env'

import imgUrl0 from '@/assets/image/img/homeworkitem/item1.png'
import imgUrl1 from '@/assets/image/img/homeworkitem/item2.png'
import imgUrl2 from '@/assets/image/img/homeworkitem/item3.png'
import imgUrl4 from '@/assets/image/img/homeworkitem/item4.png'
import imgUrl5 from '@/assets/image/img/homeworkitem/item5.png'
import imgUrl6 from '@/assets/image/img/homeworkitem/item6.png'
import imgUrl7 from '@/assets/image/img/homeworkitem/item7.png'
import imgUrl8 from '@/assets/image/img/homeworkitem/item8.png'
import imgUrl9 from '@/assets/image/img/homeworkitem/item9.png'

const getImageUrl = (index: number) => {
    return [imgUrl0, imgUrl1, imgUrl2, imgUrl4, imgUrl5, imgUrl6, imgUrl7, imgUrl8, imgUrl9][index]
}

const router = useRouter()
const route = useRoute()
function interTeachPlanManage() {
  router.push('/LessonPlan/TeachPlanManage')
};



function interPptManage() {
  router.push('/pptSquare/pptManage')
};

function handleClick(item:any) {
  if (item.title === '教案生成') {
    router.push('/LessonPlan/teachPlanTempEditor')
  }else if(item.title === 'PPT生成'){
    // myCourse/1
    window.open(`${env.VITE_PPT_FRONTEND_URL}?type=course/1`, '_blank');
  }else if(item.title === '作业管理'){
    console.log('item.title', router,item.title)
    router.push('/homework')
  }else if(item.title === '学情分析'){
    router.push('/course/courseAnalysis')
  }else if(item.title === '摘要提取'){
    router.push('/abstract')
  }else if(item.title === '章节管理'){
    router.push(`/course/chapter/${route.params.id}`)
  }
}


const items = reactive([
    { title: '作业管理' },
    { title: '考试管理' },
    { title: '章节管理' },
    { title: '学情分析' },
    { title: 'PPT生成' },
    { title: '教案生成' },
    { title: '课程班级' },
    { title: '资源推荐' },
    { title: '摘要提取' }
])

</script>
<style lang="less" scoped>
.details {
    background-image: url('@/assets/image/img/coursebg1.png');
    background-size: cover;
    background-repeat: no-repeat;
    min-height: 100vh;
    padding: 0 40px;
    overflow: auto;
    min-width: 900px;
    width: 100%;



    .title {
        font-size: 24px;
        font-weight: 700;
        line-height: 24px;
        margin: 20px 0;
    }

    .flex {
        display: flex;
        align-items: center;
    }

    .teacher {
        color: #666666;
        margin: 0 0 20px 0;
        height: 12px;
        font-size: 12px;
    }

    .konwledge {
        border-radius: 9.43px;
        background: #FFFFFF;
        border: 2px solid #FFFFFF;
        box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);
        padding: 20px;
        max-width: 1622px;
        height: 198px;

        .titlek {
            margin-bottom: 20px;
            font-weight: 700;
            font-size: 20px;
            line-height: 12px;
        }

        .num1 {
            background-image: url('@/assets/image/img/courseNum1.png');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: right;
            width: 781px;
            height: 126px;
            opacity: 1;
            border-radius: 5px;
            padding: 30px 0 0 53px;
            cursor: pointer;
        }

        .numTite {
            color: rgba(102, 102, 102, 1);
            font-size: 16px;
        }
        .numBer{
            font-size: 25px;
            font-weight: 700;
            line-height: 34.3px;
            margin-top: 11px;
        }

        .num2 {
            background-image: url('@/assets/image/img/courseNum2.png');
            background-size: cover;
            background-position: right;
            background-repeat: no-repeat;
            width: 781px;
            height: 126px;
            opacity: 1;
            border-radius: 5px;
            padding: 30px 0 0 53px;
            cursor: pointer;
        }


        .homewoke1 {
            background-image: url('@/assets/image/img/homework1.png');
        }

        .homewoke2 {
            background-image: url('@/assets/image/img/homework2.png');
            margin: 0 20px;
        }

        .homewoke3 {
            background-image: url('@/assets/image/img/homework3.png');
        }
        .homewokeCom{
            background-size: cover;
            background-position: right;
            background-repeat: no-repeat;
            width: 514px;
            height: 126px;
            border-radius: 5px;
            padding: 30px 0 0 30px;
            cursor: pointer;
        }
    }

    .items {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(253px, 1fr));
        gap: 20px;
        margin: 20px 0;
        max-width: 1622px;
        width: 100%;

        .item {
            // width: 253px;
            height: 95px;
            border-radius: 4.54px;
            background: rgba(255, 255, 255, 1);
            border: 2px solid rgba(255, 255, 255, 1);
            box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
            display: flex;
            align-items: center;
            // justify-content: center;
            cursor: pointer;


            .titleItem {
                font-weight: 600;
                // margin-left: 25px;？
                font-size: 14px;
            }

            img {
                width: 48.06px;
                height: 51.21px;
                margin: 0 30px 0 50px;
            }
        }
    }
}
</style>
