<template>
  <div class="mian">
    <back :url="'-1'" />
    <div class="top">
      <div class="title">计算机网络-作业-新建作业</div>
    </div>
    <div class="content">
      <div class="item" @click="openChoose">
        <img src="@/assets/image/course/createbg.png" style="width: 217px;height: 189px;margin-top: 33px;" />
        <div class="itemtitle">题库选题作业</div>
        <div class="remark">将题库试题发布给学生学习</div>
      </div>
      <div class="item hover" style="margin-left: 163px;">
        <img src="@/assets/image/course/createbg1.png" style="width: 217px;height: 189px;margin-top: 33px;" />
        <div class="itemtitle">知识学习作业</div>
        <div class="remark">将知识视图或知识点发布给学生学习</div>
      </div>
    </div>


    <a-modal v-model:open="open" :footer="null" :wrap-class-name="twMerge(
      '[&_.ant-modal-content]:p-[0px]!'
    )
      " width="700px">
      <div class="modal">
        <div style="height: 77px;"></div>
        <div class="modaltitle flex justify-center items-center">生成作业方式</div>
        <div class="flex justify-center" style="margin-top: 59px;">
          <img src="@/assets/image/course/aichoose.png" @click="aihandchoose"  />
          <img src="@/assets/image/course/handlechoose.png" style="margin-left: 50px;" @click="handchoose" />
        </div>
      </div>
    </a-modal>

  </div>
</template>

<script lang="ts" setup>
import { useCourseStore } from '@/stores/course'
const courseStore = useCourseStore()
const { courseId,user_id } = toRefs(courseStore)

import { creatExam } from '@/services/api/exam'
import { twMerge } from 'tailwind-merge'
import back from '@/components/ui/back.vue';
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router'
const router = useRouter()
const open = ref(false);
function openChoose() {
  open.value = true;
}

async function handchoose(){
  const params ={
    course: courseId.value,//
    // author: user_id.value
  }
  const res = await creatExam(params)
  console.log(res,'新建作业')
  courseStore.setHomeworkDetails(res.data)
  router.push({path:'/homework/handquetion'})
}
function aihandchoose(){
  router.push('/question/ailist')
}

</script>
<style lang="scss" scoped>
.mian {
  min-height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center bottom;
  min-width:907px;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    font-size: 24px;
    font-weight: 700;
    line-height: 12px;
    margin: 20px 0;
  }
}

.content {
  display: flex;
  justify-content: center;
  align-items:center;
  min-width: 856px;
  height: calc(100vh - 116px);
}

.item {
  width: 372px;
  height: 361px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;

  .itemtitle {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 12px;
    color: rgba(0, 0, 0, 1);
    margin-top: 26px;
  }

  .remark {
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 30px;
    color: rgba(102, 102, 102, 1);
    margin-top: 21px;
  }
}

.hover {
  filter: grayscale(100%);
}
.modal{
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('@/assets/image/course/modelbg.png');
  width: 700px;
  height: 445px;
  opacity: 1;
  .modaltitle{
    margin: 0 auto;
    background-image: url('@/assets/image/course/xubg.png');
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.14) 0%, rgba(255, 255, 255, 0.6) 22.91%, rgba(255, 255, 255, 1) 67.72%, rgba(255, 255, 255, 0) 100%);

// border: 1px solid rgba(255, 255, 255, 1);

    width: 378px;
    height: 47px;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0px;
    line-height: 39px;
    color: rgba(41, 86, 150, 1);
  }
  img{
    width: 180px;
    height: 150px;
    cursor: pointer;
  }
// background: linear-gradient(210.8deg, rgba(250, 252, 255, 1) 0%, rgba(191, 222, 255, 1) 100%);
}
</style>