<template>
    <div class="page-content">
        <div class="">
            <back :url="'-1'"></back>
            <div class=" flex justify-between items-center">
                <div class="text-[24px] leading-[24px] font-bold my-[20px]">计算机网络-我的题库</div>
                <div class="flex items-center gap-[14px]">
                    <AButton
                        type="primary"
                        class="gradient-a-button w-[82px]"
                        @click="addhandleQuetion"
                        >
                        添加试题
                    </AButton>
                    <AButton
                        type="primary"
                        class="gradient-a-button w-[82px]"
                        @click="addAIQuetion"
                        >
                        AI生题
                    </AButton>
                </div>
            </div>
            <div class="quetion ">
                <div class="search">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-5 flex-wrap">
                            <a-select v-model:value="course" style="width: 237px;" size="small" :options="optionsCourse"
                                placeholder="关联课程" clearIcon @change="changeCourse" />
                            <a-select v-model:value="examUser" style="width: 237px;" size="small" :options="optionsUser"
                                placeholder="归属人"></a-select>
                            <a-date-picker show-time v-model:value="params.created_at" placeholder="创建时间"
                                style="width: 237px;" size="small" format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss" @change="getquestionList" />

                            <a-select :disabled="course == 0" v-model:value="konwledge" mode="multiple"
                                style="width: 237px" placeholder="知识点" size="small" :options="konwledgeList"
                                clearIcon  @change="changeKonwledge"/>

                            <a-input placeholder="输入关键字" v-model:value="params.stem" size="small" style="width: 237px;"
                                @pressEnter="getquestionList">
                                <template #suffix>
                                    <SearchOutlined style="color: #C4C4C4;" @click="getquestionList" />
                                </template>
                            </a-input>
                        </div>

                    </div>
                    <div class="flex justify-between items-end">
                        <div class="flex align-center mt-[17px] ">
                            <div class="w-[42px] text-[#333333]">题型：</div>
                            <div>
                                <a-radio-group v-model:value="params.type" @change="changeRadio">
                                    <a-radio :value="''">全选</a-radio>
                                    <a-radio :value="'单选题'">单选题</a-radio>
                                    <a-radio :value="'多选题'">多选题</a-radio>
                                    <a-radio :value="'填空题'">填空题</a-radio>
                                    <a-radio :value="'判断题'">判断题</a-radio>
                                    <a-radio :value="'问答题'">问答题</a-radio>
                                </a-radio-group>
                            </div>
                        </div>
                        <div class="text-[14px] text-[#666666]">
                            共有<span class="text-[#3F8CFF]">{{ questionLists.length }}</span>个筛选结果
                        </div>

                    </div>
                </div>
                <div v-if="questionLists.length > 0">
                    <div class="flex justify-between items-center">
                        <div class="flex gap-[20px] items-center">

                            <a-checkbox  v-model:checked="state.checkAll"
                               :indeterminate="state.indeterminate">
                                全选
                            </a-checkbox>
                            <AButton
                                type="primary"
                                ghost
                                class="outline-a-button"
                                @click="awayQuetion"
                                >
                                {{ isOpen ? '收起题目详情' : '展开题目详情' }}
                            </AButton>
                        </div>
                        <div class="flex gap-[16px]">
                            <AButton
                                type="primary"
                                ghost
                                class="outline-a-button"
                                >
                                <div class="flex items-center">
                                    <img src="@/assets/image/img/knowledgeicon.png" alt="" class="w-[14px] h-[14px]">
                                    <div class="ml-[8]">修改关联知识点</div>
                                </div>
                            </AButton>

                            <AButton
                                type="primary"
                                ghost
                                class="outline-a-button w-[80px]"
                                @click="deleteQuestion"
                                >
                                <div class="flex items-center">
                                    <img src="@/assets/image/img/blueDel.png" alt="" class="w-[14px] h-[14px]">
                                    <div class="ml-[3]">删除</div>
                                </div>
                            </AButton>
                        </div>
                    </div>
                    <!-- 试题组件 -->
                    <Item :isOpen="isOpen" :questionLists="questionLists" @deleteQue="getquestionList"
                        @editQue="editQuetion" @chooseQue="chooseQuetion" :chooseType="'quetion'"></Item>
                </div>
                <div v-else>
                    <img src="@/assets/image/zanwu/nodata.png" style="width: 309px;height: 301px;margin: auto;" />
                </div>
            </div>

            <!-- 分页组件 -->
            <div class="pagination">
                <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
                    layout="->, prev, pager, next" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </div>

        <!-- 新增试题弹窗 -->
        <addQution :open="openAddQution" @update:open="val => openAddQution = val" @update:success="getquestionList" />
        <!-- 编辑试题弹窗 -->
        <EditQuetion :editType="'handleEdit'" :open="openEditQution" :quetionDetails="quetionDetails"
            @update:open="val => openEditQution = val" @update:success="getquestionList" />
    </div>
</template>

<script lang="ts" setup>
const plainOptions = ref<any[]>([]);
const questionIds = ref<any[]>([]);
const state = reactive({
    indeterminate: false,
    checkAll: false,
    checkedList: [],
});
watch(() => plainOptions, (checked) => {
    if (checked.value.length > 0) {
        state.indeterminate = true;
    } else if (questionIds.value.length == checked.value.length) {
        state.checkAll = true;
    } else {
        state.indeterminate = false;
    }
}, { deep: true });
watch(() => state.checkAll, (checked) => { 
    if (checked) {
        plainOptions.value = questionIds.value;
    } else {
        plainOptions.value = [];
    }
    questionLists.value.map((item: any) => {
        item.checked = checked;
        return item;
    })
}, { deep: true });


import { usePagination } from '@/hooks/usePagination'; //解构分页参数
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getquestionList);
import addQution from '@/components/question/addQution.vue';
import EditQuetion from '@/components/question/EditQuetion.vue';
import Item from '@/components/question/Item.vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import Back from '@/components/ui/back.vue'
import { reactive, ref, h, onMounted, watch } from 'vue';
import { questionList,deleteQuestionsBatch, questionUserList } from '@/services/api/question';
import { courseOwner, knowledgeList } from '@/services/api/course';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { message } from 'ant-design-vue';

interface Option {
    value: number;
    label: string;
}
//关联课程
const course = ref(0)
const optionsCourse = ref([{
    value: 0,
    label: '未关联课程'
}])

//归属人
const examUser = ref()
const optionsUser = ref<Option[]>([]);
function getquestionUserList() {
    // questionUserList().then((data:any) => {
    //     // data.data.forEach((item: any) => {
    //     //     optionsUser.value.push({
    //     //         value: item.id,
    //     //         label: item.username
    //     //     })
    //     // })
    // })
}
//知识点
const handleChange = (value: string[]) => {
    console.log(`selected ${value}`);
};
const konwledge = ref<string[]>([]);
const konwledgeList = ref<Option[]>([])
//控制选项是否展开isOpen
const isOpen = ref(false)
function awayQuetion() {
    isOpen.value = !isOpen.value
}


//删除题目单一
async function deleteQuestion() {
    const confirmed = await showDeleteConfirm('确定删除多条记录吗？删除后无法恢复！');
    if (confirmed) {
        const params={
            "ids": plainOptions.value  //传入需要删除的id列表
        }
        deleteQuestionsBatch(params).then((res:any) => {
            console.log(res);
            message.success(res?.message || '删除成功')
            state.indeterminate = false
            getquestionList()
        })
    }
}

//选择试题
function chooseQuetion(data: any) {
    if (plainOptions.value.includes(data.id)) {
        plainOptions.value = plainOptions.value.filter(item => item !== data.id)
    } else {
        plainOptions.value.push(data.id)
    }
}


const quetionDetails = ref() //编辑试题的数据
//子组件传过来编辑的数据
function editQuetion(data: any) {
    openEditQution.value = true
    quetionDetails.value = data
}

const openAddQution = ref(false)
const openEditQution = ref(false)
//手动添加试题
function addhandleQuetion() {
    openAddQution.value = true
}
import { useRouter } from 'vue-router'
const router = useRouter()
function addAIQuetion() {
    router.push({
        path: '/question/ailist',
        query: {
            type: 1//从题库进入
        }
    })
}

const params = reactive({
    stem: '',
    type: '',
    created_at: '',
    course_id: course.value == 0 ? '' : course.value,
    knowledge_point: konwledge.value,
})
const questionLists = ref([]) //题目列表
async function getquestionList() {
    state.indeterminate = false
    state.checkAll = false

    const param = {
        page: currentPage.value,
        page_size: pageSize.value,
        ...params
    }
    questionList(param).then((res: any) => {
        if (res.code == 200) {
            total.value = res.data.count
            // questionLists.value = res.data.results.filter((item:any) => !item.is_deleted)
            questionLists.value = res.data.results.map((item: any) => {
                return {
                    ...item,
                    checked: false
                }
            })
            console.log(questionLists.value, 'questionLists.value')
            questionIds.value = res.data.results.map((item: any) => item.id)
        }
    }).catch(error => {
        console.error('获取失败:', error)
    })
}
function changeRadio(e: any) {
    console.log('changeRadio', e)
    getquestionList()
}

function getcourseOwner() {
    // const data = [{ id: 1, title: '建筑学' }]
    courseOwner().then((data:any) => {
        data.data.forEach((item: any) => {
            optionsCourse.value.push({
                value: item.id,
                label: item.title
            })
        })
    })
}
function changeCourse(e: any) {
    params.course_id = e
    if(e==0){
        konwledge.value = []
    }
    getquestionList()
    getknowledgeList(e)
}
function getknowledgeList(e: any) {
    const params = {
        course_id: e
    }
    // const data = [{ id: 1, name: '计算机网络' }]
    console.log(params, 'params')
    knowledgeList(params).then((data:any) => {
        data.data.forEach((item: any) => {
            konwledgeList.value.push({
                value: item.id,
                label: item.name
            })
        })
    })
}
function changeKonwledge(e: any) {
    params.knowledge_point = e
    console.log('changeKonwledge', e)
    getquestionList()
}

getquestionList()
getcourseOwner()//课程列表
getquestionUserList()//归属人列表
</script>

<style scoped lang="scss">

:deep(.ant-radio-wrapper){
    color: rgba(0, 0, 0, 0.65) !important;
    margin-right: 27px !important;
}
:deep(.ant-picker){
    border-radius: 4px !important;
}

.page-content {
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: cover;
    box-sizing: border-box;
    padding: 0 40px;
    overflow: auto;
    min-height: 100vh;
}

.quetion {
    min-width: 880px;
    // margin: 20px 0 0;
    border-radius: 5px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
    height: calc(100vh - 130px - 32px);
    padding: 20px 20px 20px 20px;
    overflow: auto;
}

.search {
    // height: 97px;
    opacity: 1;
    border-radius: 5px;
    background: rgba(63, 140, 255, 0.03);
    padding: 20px 20px 20px 20px;
    margin-bottom: 12px;
}

.pagination {
    text-align: center;
}

:deep(.el-pager li) {
    min-width: 22px !important;
    height: 22px !important;
    background-color: transparent !important;
    /* 完全透明 */
}

:deep(.el-pager li.is-active) {

    background-color: #3f8cff !important;
    /* 完全透明 */
    font-size: 14px;
    color: #ffffff;
    //   background-color: #3f8cff;
}

:deep(.btn-prev) {
    background-color: transparent !important;
    /* 完全透明 */
}

:deep(.btn-next) {
    background-color: transparent !important;
    /* 完全透明 */
}
</style>
