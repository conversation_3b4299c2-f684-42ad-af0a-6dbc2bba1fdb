<template>
  <div class="page">

      <!-- <div @click="handleBack" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div> -->
      <h1 class="text">知识图谱</h1>
      <div class="toolbar">
        <div class="toolbar-left">
          <el-select v-model="valueSelect" placeholder="课程选择" style="width: 100px;min-height: 32px;"
            suffixIcon="CaretBottom">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-input v-model="searchValue" style="width: 302px;min-height: 32px;" placeholder="输入标题查询" >
            <template #suffix>
              <el-icon ><Search /></el-icon>
            </template>
          </el-input>
          <div class="res-text">共有<text style="color: rgba(63, 140, 255, 1);">6</text>个筛选结果</div>
        </div>
        <div class="toolbar-right">
            <AButton
                type="primary"
                class="gradient-a-button w-[82px]"
                @click="open = true"
                >
                新建
            </AButton>
            <AButton
                type="primary"
                class="gradient-a-button w-[82px]"
                >
                删除
            </AButton>
        </div>
      </div>
      <a-spin :spinning="spinning">
        <div class="table">
          <div class="table-head height" style="font-weight:600">
            <div class="item" style="max-width: 56px;">
              <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                @change="onCheckAllChange" />
            </div>
            <div class="item" style="width: 400px;">知识图谱名称</div>
            <div class="item" style="width: 130px;">知识点数量</div>
            <div class="item" style="width: 140px;text-align: center;">状态</div>
            <div class="item" style="max-width: 118px;text-align: center;">操作</div>
          </div>

          <div class="center" v-if="files?.length === 0">
            <img src="@/assets/image/zanwu/nodata.png" style="width: 385px;height: 379px;" />
          </div>
          <div v-else>
            <div class="table-head height1" v-for="item in files" :key="item.id">
              <div class="item" style="max-width: 58px;">
                <a-checkbox :checked="selectedIds.includes(item.id)"
                  @change="(e: any) => handleCheckboxChange(item.id, e.target.checked)" />
              </div>
              <div class="item file-name titlew">
                <div class="file-name-text">
                  {{ item.name }}
                </div>
                <!-- <span class="edit-icon" style="">
                  <a-tooltip placement="bottom">
                    <template #title>
                      <span>编辑</span>
                    </template>
                    <img style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/image/img/edittable.png" @click="editGraph(item)" />
                  </a-tooltip>
                </span> -->
              </div>
              <div class="item" style="width: 130px;">
                {{ item.node_count === 'null' || item.node_count == null ? '无' : item.node_count}}
              </div>
              <div class="item" style="width: 140px; display:flex; justify-content:center;">
                {{ item.status }}
              </div>
              <div class="item" style="max-width:118px; display:flex; justify-content:center; gap:30px">
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>关联</span>
                  </template>
                  <img style="cursor: pointer;" src="@/assets/image/graph/relation.png"  />
                </a-tooltip>
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>删除</span>
                  </template>
                  <img style="cursor: pointer;" src="@/assets/image/graph/delete.png"  />
                </a-tooltip>
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>查看</span>
                  </template>
                  <img style="cursor: pointer;" src="@/assets/image/graph/eye.png" @click="watch()"/>
                </a-tooltip>

              </div>
            </div>
          </div>

          <div class="pagination">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
              layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </a-spin>

        <a-modal v-model:open="open" :title="modelTitle" :footer="null" width="700px">
            <div style="margin: 50px 0 0 0;">
                <a-form :model="graphForm" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
                    autocomplete="off">
                    <a-form-item
                        label="课程名称" 
                        name="title" 
                        v-bind="validateInfos.title"
                        :rules="[{ required: true, message: '请输入课程名称!' }]"
                    >
                        <a-input v-model:value="graphForm.title" placeholder="请输入课程名称" />
                    </a-form-item>


                    <a-form-item
                        label="上传附件" 
                        name="file" 
                        :rules="[{ required: true, message: '请上传附件!' }]"
                    >

                    <div style="display: flex; align-items: center; gap:8px">
                        <a-upload :file-list="fileList" :maxCount="1" accept=".doc,.docx"  @remove="handleRemove" :before-upload="beforeUpload">
                            <a-button style="width: 79px;height:32px;color: #666; display: flex; justify-content: center; align-items: center;">
                                <UploadOutlined />文件            
                            </a-button>
                        </a-upload>

                        <div style="flex:1; height: 17px; display: flex; font-size: 12px; line-height: 16.64px; gap:5px">
                            <div style="display: inline-flex;justify-content: center;">
                                <ExclamationCircleOutlined/>
                            </div>
                            <span>
                                支持PDF、DOC、DOCX、PPT、PPTX,单个文件大小不超过500MB
                            </span>
                        </div>
                    </div>

                    </a-form-item>

                    <div style="display: flex;justify-content: flex-end">
                        <a-form-item>
                        <a-space>
                            <a-button @click="open = false">取消</a-button>
                            <a-button type="primary" html-type="submit">确定</a-button>
                        </a-space>
                    </a-form-item>
                    </div>
                </a-form>
            </div>
        </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/util'
//引入接口
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
import { ref, computed, onMounted, reactive, } from 'vue'
import { Search,} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import { graphList,createGraph } from "@/services/api/graph";
import { Form } from 'ant-design-vue';
import { UploadOutlined,ExclamationCircleOutlined} from '@ant-design/icons-vue';
import type { UploadProps } from 'ant-design-vue';
import { boolean } from 'zod';

//弹窗标题
const modelTitle = ref('创建课程') 
//弹窗开关
const open = ref<boolean>(false);
const graphForm = reactive({
    title: '',
    file: false
});

const useForm = Form.useForm
const { validateInfos } = useForm(graphForm);//验证表单提交

const fileList = ref<UploadProps['fileList']>([])
const fileValue = ref<File>();
const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};
const beforeUpload: UploadProps['beforeUpload'] = file => {
  fileList.value = [file];
  fileValue.value = file;
  graphForm.file = true
  return false;
};


const router = useRouter()

//当前页所有id
const currentPageIds = computed(() => files.value?.map(item => item.id) || []);
// 已选中的id
const selectedIds = ref<number[]>([]);
// 处理单个复选框的选择变化
const handleCheckboxChange = (id: number, checked: boolean) => {
  if (checked) {
    selectedIds.value.push(id);
  } else {
    selectedIds.value = selectedIds.value.filter((item) => item !== id);
  }
};
// 全选操作
const onCheckAllChange = (e: any) => {
  const isChecked = e.target.checked;
  if (isChecked) {
    selectedIds.value = [...new Set([...selectedIds.value, ...currentPageIds.value])]; // 合并去重
  } else {
    selectedIds.value = selectedIds.value.filter(id => !currentPageIds.value.includes(id));
  }
  state.indeterminate = false;
};
const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
});


// 模拟数据
const files = ref<any[]>()
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getGraphList);
const spinning = ref(false)

const valueSelect = ref(null)
const options = reactive([
  { value: 1, label: '章节一' },
  { value: 2, label: '章节二' },
  { value: 3, label: '章节一' },
  { value: 4, label: '章节二' },
])
const searchValue = ref(null)

onMounted(() => {
  getGraphList() //  获取教案列表
})

// 获取图谱列表
async function getGraphList () {
  const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      name: searchValue.value
  }
  try {
    spinning.value = true
    const res = await graphList(params)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false

  } catch (error) {
    console.error('获取知识图谱列表失败:', error)
  }
}


const handleBack = () => {
  //需要动态配置课程id
  router.back()
}

const watch = () => {
  // 跳转到查看页面
  router.push('/course/courseAnalysis_1')
}


</script>

<style scoped lang="scss">
:deep(.el-input__suffix-inner) {
    cursor: pointer;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 40px;

  .back-btn {
    cursor: pointer;
    width: 60px;
    margin-top: 50px;
    color: #333;
    display: flex;
    align-items: center;
  }

}

.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 100px 0 20px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  min-width: 900px;

  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
    .res-text{
        margin-left: 15px;
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 0px;
        color: rgba(102, 102, 102, 1);
    }

  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;

    .addbut {
      width: 90px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }
  }
}

:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}


.table {
  background: #fff;
  border-radius: 4.79px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px;
  min-width: 900px;
  width: 100%;


  .table-head {
    // display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* 禁止换行 */
    overflow-x: auto;
    /* 允许水平滚动（如果需要） */
    font-size: 14px;
    padding-left: 12px;

    .item {
      flex: 1 0 auto;
      white-space: nowrap;
      /* 防止文字换行 */
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .titlew {
      width: 400px;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .file-name-text {
      max-width: 330px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden
    }

    .file-name .edit-icon {
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      left: 345px;
      top: 50%;
      transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
      opacity: 1;
    }
  }

  .height {
    height: 56px;
    border-bottom: 1px solid #E5E5E5;
  }

  .height1 {
    height: 58px;
    border-bottom: 1px solid #E5E5E5;
    color: #666666;

    &:hover {
      background: rgba(63, 140, 255, 0.05);
    }
  }
}

.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
</style>
