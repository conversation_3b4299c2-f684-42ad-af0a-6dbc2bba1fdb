<template>
  <a-spin :spinning="spinning">
    <div class="page">
        <div class="toolbar">
          <div class="toolbar-text">
              <span>知识图谱</span>
          </div>
          <div class="toolbar-buttons">
              <a-button type="primary" ghost style="width: 100px;">网状图</a-button>
              <a-button type="primary" ghost style="width: 100px;">树状图</a-button>
              <a-button type="text" class="addbut">保存</a-button>
          </div>
        </div>
        
        <div class="box-container">
          <div class="box">
            <a-textarea
              v-model:value="keyword"
              placeholder="请输入文字提取知识图谱"
              class="textarea"
              :bordered="false"
            />
          </div>

          <div class="box">
              <div class="graph-wrapper" :class="{ fullscreen: isenlarge }">
                  <!-- 控制面板，包含全屏切换按钮和2D/3D切换按钮 -->
                  <div class="control-panel">
                      <span class="enlarge-button" :class="{'enlarge-button_full_screen':isenlarge}" @click="toggleEnlarge">
                          <el-icon><Rank /></el-icon>
                      </span>
                      <!-- <span class="control-button" :class="{'control-button-fullscreen': isenlarge}" @click="toggle2D3D">
                      {{ is3D ? '2D' : '3D' }}
                      </span> -->
                  </div>

                  <!-- 根据 is3D 状态切换显示 2D 或 3D 组件 -->
                  <Graph2D 
                    v-if=" !is3D && (staticNodes.length != 0 || staticLinks.length !=0)" 
                    :nodes="staticNodes" 
                    :links="staticLinks" 
                    :getGraphList="getGraphList"
                    @deleteNode="handleDeleteNode" 
                    @addNode="addNode"
                    @updateNode="updateNode"
                    @updateEdge="updateEdge"
                  />
                  <!-- <Graph3D v-else :nodes="staticNodes" :links="staticLinks" :getGraphList="getGraphList"/> -->
              </div>
          </div>
        </div>


        <el-dialog v-model="dialogVisible" :title=dialogTitle width="500">
            <el-form :model="form">
              <el-form-item v-show="showLabel" label="标签">
                <el-input v-model="form.label" autocomplete="off" />
              </el-form-item>
              <el-form-item v-show="showName" label="名称">
                <el-input v-model="form.name" autocomplete="off" />
              </el-form-item>
              <el-form-item v-show="showContent" label="内容">
                <el-input type="textarea" autosize v-model="form.content" autocomplete="off"/>
              </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                  <el-button @click="dialogVisible = false">取消</el-button>
                  <el-button type="primary" @click="handleConfirm">
                    确认
                  </el-button>
                </div>
            </template>
        </el-dialog>

        <div class="toolbar-buttons">
              <a-upload :file-list="fileList" :maxCount="1" accept=".doc,.docx"  @remove="handleRemove" :before-upload="beforeUpload">
                <a-button type="primary" ghost style="width: 100px; display: flex; justify-content: center; align-items: center;">
                  附件<PaperClipOutlined />              
                </a-button>
              </a-upload>
              <a-button type="text" class="addbut" style="width: 100px; padding: 2px;" @click="getGraph">生成知识图谱</a-button>
        </div>  

    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ref, onMounted, provide } from "vue";
import { graphList } from "@/services/api/graph";
import Graph2D from '@/components/graph/Graph2D.vue';
import Graph3D from '@/components/graph/Graph3D.vue';
import { PaperClipOutlined } from '@ant-design/icons-vue';
import { createGraph, addGraphNode, updateGraphNode, updateGraphEdge } from "@/services/api/graph";
import { message } from 'ant-design-vue';
import type { UploadProps } from 'ant-design-vue';

const spinning = ref(false);
const keyword = ref('')

const fileList = ref<UploadProps['fileList']>([])
const fileValue = ref<File>();
const handleRemove: UploadProps['onRemove'] = file => {
  fileList.value = [];
};

const beforeUpload: UploadProps['beforeUpload'] = file => {
  fileList.value = [file];
  fileValue.value = file;
  return false;
};

const getGraph = async () => {
  spinning.value = true;
  const params = {
    theme: keyword.value,
    file: fileValue.value
  };
  try {
    const res = await createGraph(params);
    spinning.value = false;
    message.success('生成知识图谱成功！');
    getGraphList()
  } catch (error) {
    message.error('生成知识图谱失败');
    console.error('生成知识图谱失败:', error);
  }
}

const dialogVisible = ref(false)
const dialogTitle =ref("")

const router = useRouter()

const handleBack = () => {
  router.back()
}

// 控制是否全屏显示的状态
const isenlarge = ref(false);
// 控制是否显示3D渲染的状态
const is3D = ref(false);

// 存储从 API 获取的节点和链接数据
const staticNodes = ref<any[]>([]);
const staticLinks = ref<any[]>([]);

// 获取知识图谱数据列表
const getGraphList = async () => {
    try {
        const res: any = await graphList(); // 调用 API
        console.log('获取知识图谱列表成功', res.data);
        if (res.data && res.data.nodes && res.data.edges) {
          console.log('Nodes:', res.data.nodes);
          console.log('Edges:', res.data.edges);
          staticNodes.value = res.data.nodes;
          staticLinks.value = res.data.edges.map((edge: any) => ({
              ...edge,
              source: edge.source.toString(), // 确保 source 是字符串
              target: edge.target.toString()  // 确保 target 是字符串
          }));
          // 打印转换后的数据进行调试
          console.log('转换后的 staticNodes:', staticNodes.value);
          console.log('转换后的 staticLinks:', staticLinks.value);
        }
    } catch (error) {
        console.error('获取知识图谱列表失败:', error);
    }
}

// 切换全屏显示
const toggleEnlarge = () => {
    isenlarge.value = !isenlarge.value;
};

// 切换2D/3D显示
const toggle2D3D = () => {
    is3D.value = !is3D.value;
    console.log('切换到 3D:', is3D.value); // 打印状态，用于调试
};

const form = reactive({
    label: '',
    name: '',
    content: '',
})
const emitMode = ref('');
const emitId = ref(0);

const handleConfirm = async() =>{
  dialogVisible.value = false
  if ( emitMode.value === "添加节点" ){
    let params = {
      "label": form.label,
      "userID": 66,
      "properties": {
          "name": form.name,
          "content": form.content
      }
    };
    const response = await addGraphNode(params);
    console.log('添加节点成功', response);
    message.success('添加节点成功！');
    getGraphList()
  }
  else if ( emitMode.value === "编辑节点" ){
    let params = {
      "vertex_id": emitId.value,
      "properties": {
          "name": form.name,
          "content": form.content
      }
    };
    console.log(params,"params");
    
    const response = await updateGraphNode(params);
    console.log('编辑节点成功', response);
    message.success('编辑节点成功！');
    getGraphList()
  }
  else if ( emitMode.value === "编辑边" ){
    let params = {
      "edge_id": emitId.value,
      "label": form.label,
    };
    const response = await updateGraphEdge(params);
    console.log('编辑边成功', response);
    message.success('编辑边成功！');
    getGraphList()
  }
}

const showLabel = ref(true);
const showName = ref(true);
const showContent = ref(true);

// 处理子组件添加节点事件
const addNode = ( { mode }: { mode: string }) => {
    showLabel.value = true;
    showName.value = true;
    showContent.value = true;

    form.label = '';
    form.name = '';
    form.content = '';

    emitMode.value = mode;
    dialogVisible.value = true;
    dialogTitle.value = "添加节点";
};


// 处理子组件编辑节点事件
const updateNode = ( { mode, id, name, content }: { mode: string, id: number, name: string, content:string } ) => {
    showLabel.value = false;
    showName.value = true;
    showContent.value = true;

    form.name = name;
    form.content = content;

    emitMode.value = mode;
    emitId.value = id;
    dialogVisible.value = true;
    dialogTitle.value = "编辑节点";
};

// 处理子组件编辑边事件
const updateEdge = ( { mode, id, label}: { mode: string, id: number, label:string } ) => {
    showLabel.value = true;
    showName.value = false;
    showContent.value = false;
    form.label = label;

    emitMode.value = mode;
    emitId.value = id;
    dialogVisible.value = true;
    dialogTitle.value = "编辑边";
};

// 处理子组件删除节点或边事件
const handleDeleteNode = async ({ nodes, links }: { nodes: any[], links: any[] }) => {
    // 更新本地数据
    // staticNodes.value = nodes;
    // staticLinks.value = links;
    getGraphList()
};

//定义增加节点事件
provide('refreshGraphData', getGraphList);

onMounted(async () => {
    console.log("组件挂载完成");
    await getGraphList(); // 等待数据加载完成
});



</script>


<style scoped lang="scss">
:deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item){
  bottom: 65px;
  right: 0vw;
  width: 200px;
}
:deep(.ant-upload-list-item-container){
  width: 0;
  height: 0;
}

.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; 
  background: url(@/assets/image/graph/graph_bg.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 90px 40px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.toolbar-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

.toolbar-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  .addbut {
    width: 100px;
    color: #fff;
    background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
  }
}


.box-container {
  width:100%;
  height: 750px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0 30px 0;
  gap:20px;
}

.box {
  flex: 1;
  height: 100%;
  border-radius: 0.2vw;
  background-color: white;
}

.textarea {
  height: 100%;
  background: transparent;
  padding: 12px;
}

/* 图形包装器样式 */
.graph-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

/* 确保全屏模式下的尺寸正确 */
.fullscreen {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 1; 
}

/* 控制面板样式 */
.control-panel {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1; /* 确保控制面板始终在最上层 */
}

/* 放大按钮样式 */
.enlarge-button {
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5); /* 添加半透明背景 */
    padding: 5px;
    border-radius: 4px;
}

/* 全屏模式下的放大按钮样式 */
.enlarge-button_full_screen {
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5); /* 添加半透明背景 */
    padding: 5px;
    border-radius: 4px;
}

/* 控制按钮样式 */
.control-button {
    width: 3rem;
    height: 3rem;
    text-align: center;
    line-height: 2rem;
    color: #bbb;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px;
    border-radius: 4px;
    display: inline-block;
    margin-left: 10px;
}

/* 全屏模式下的控制按钮样式 */
.control-button-fullscreen {
    color: #fff;
    /* margin-top: 8vh; */
    /* margin-right: 20vw; */
}
</style>