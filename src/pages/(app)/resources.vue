<template>
  <div class="page">
    <div class="top-section">
      <h1 class="page-title">资源检索</h1>
	  <div class="switch-but">
		<a-radio-group v-model:value="switchType">
			<a-radio-button value="inside">站内</a-radio-button>
			<a-radio-button value="outside">站外</a-radio-button>
		</a-radio-group>
	  </div>

      <div class="search-bar">
        <a-select
		  v-show="showInside"
          class="search-select"
          v-model:value="selected"
          :suffix-icon="customIcon"
          @change="handleChange"
          :bordered="false"
          :dropdown-match-select-width="false"
          :dropdown-style="{ width: '60px'}"
        >
          <a-select-option value="resource">资源</a-select-option>
          <a-select-option value="paper">论文</a-select-option>
        </a-select>

        <a-input
          v-model:value="keyword"
          placeholder="请输入检索的关键字"
          class="search-input"
          :bordered="false"
          @pressEnter="onSearch"
        />

        <SearchOutlined class="search-icon" @click="onSearch" />
      </div>
    </div>

    <div v-show="showInside" v-if="showResource" class="main-resource">
      <div class="sidebar">

        <div class="top">
          <div class="title">筛选条件</div>
              <div class="buts">
                <a-button @click="clearFilters" style="color: rgba(63, 140, 255, 1);">清空</a-button>
                <a-button type="primary" @click="onSearch">筛选</a-button>
              </div>
        </div>

        <div class="filter-group">
          <div class="filter-title">
            文件类型 
            <CaretDownOutlined @click="handleShowFile" />
          </div>
          

          <div v-show="showFile" style="display: flex; flex-direction: column; gap: 12px;">
            <!-- 全选复选框（单独处理） -->
            <div style="display: flex;">
              <a-checkbox
                :checked="checkAll_1"
                :indeterminate="indeterminate_1"
                @change="onCheckAllChange_1"
              >              
              </a-checkbox>
              <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
                <span>全部 </span>
                <span>（{{ totalCount_1 }}）</span>
              </div>
            </div>

            <!-- 子项复选框组 -->
            <a-checkbox-group :value="checkedList_1" @change="onGroupChange_1" style="width: 100%;">
              <div style="display: flex; flex-direction: column; gap: 12px; width: 100%;">
                <div style="display: flex; " v-for="item in options_1" :key="item.value" >
                  <a-checkbox :value="item.value" />
                  <div style="flex:1; display: flex; justify-content: space-between; align-items: center; margin-left: 8px; ">
                    <span>{{ item.label }}</span>
                    <span style="margin-left: 8px;">（{{ item.count }}）</span>
                  </div>
                </div>
              </div>
            </a-checkbox-group>
          </div>


        </div>

        <div class="filter-group">
          <div class="filter-title">
            院系专业
            <CaretDownOutlined @click="handleShowDepartment"/>
          </div>
          
          <div v-show="showDepartment" style="display: flex; flex-direction: column; gap: 12px;">
            <!-- 全选复选框（单独处理） -->
            <div style="display: flex;">
              <a-checkbox
                :checked="checkAll_2"
                :indeterminate="indeterminate_2"
                @change="onCheckAllChange_2"
              >              
              </a-checkbox>
              <div style="flex:1; display: flex; justify-content: space-between;margin-left: 8px;">
                <span>全部 </span>
                <span>（{{ totalCount_2 }}）</span>
              </div>
            </div>

            <!-- 子项复选框组 -->
            <a-checkbox-group :value="checkedList_2" @change="onGroupChange_2" style="width: 100%;">
              <div style="display: flex; flex-direction: column; gap: 12px; width: 100%;">
                <div style="display: flex; " v-for="item in options_2" :key="item.value" >
                  <a-checkbox :value="item.value" />
                  <div style="flex:1; display: flex; justify-content: space-between; align-items: center; margin-left: 8px; ">
                    <span>{{ item.label }}</span>
                    <span style="margin-left: 8px;">（{{ item.count }}）</span>
                  </div>
                </div>
              </div>
            </a-checkbox-group>
          </div>

        </div>

        <div class="filter-group">
          <div class="filter-title">
            过滤时间
            <CaretDownOutlined @click="handleShowDate"/>
          </div>
          <a-range-picker v-show="showDate" v-model:value="dateValue" valueFormat="YYYY-MM-DD" style="width: 100%;"/>
        </div>

      </div>

      <div class="content">
        <div class="result-title">
          搜索结果 
          <span class="res-text">共有 <span style="color: rgba(63, 140, 255, 1);">{{ resCount }}</span> 个筛选结果</span>
        </div>
        <div class="no-data" v-if="resCount === 0">
          <img src="@/assets/image/resources/nodata.png" alt="暂无资源" />
        </div>
        <div class="show-data" v-else>
            <!-- <div v-for="(item, index) in fileList" :key="index" @click="selectItem(index)">
              {{ item.title }}
            </div> -->
            <Preview v-model:show="showPreDialog" :item="previewItem"/>

			<div class="table">
				<div class="table-content">
					<div v-for="(item, index) in fileList" :key="index" class="table-item" @click="selectItem(item)">
						<div class="table-column" style="flex-shrink: 0;">
							<img v-if="item.file_type==='word'" src="@/assets/image/resources/Doc.png" class="icon">
							<img v-if="item.file_type==='pdf'" src="@/assets/image/resources/PDF.png" class="icon">
							<img v-if="item.file_type==='ppt'" src="@/assets/image/resources/PPT.png" class="icon">
							<img v-if="item.file_type==='video'" src="@/assets/image/resources/Video.png" class="icon">
							<img v-if="item.file_type==='image'" src="@/assets/image/resources/Photo.png" class="icon">
						</div>
						<div class="table-column">
							<div class="resource-text">{{ item.title }}</div>
						</div>
					</div>
				</div>
				<div class="pagination">
					<el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="totalPage"
					layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
				</div>
			</div>

        </div>
      </div>
    </div>

    <div v-show="showInside" v-else class="main-paper">
      <div class="content">
		
        <div class="result-title">
          搜索结果 
          <span class="res-text">共有 <span style="color: rgba(63, 140, 255, 1);">{{ resCount }}</span> 个筛选结果</span>
        </div>
        <div class="no-data" v-if="resCount === 0">
          <img src="@/assets/image/resources/nodata.png" alt="暂无资源" />
        </div>
        <div class="show-data" v-else>
            <!-- <div v-for="(item, index) in fileList" :key="index" @click="selectItem(index)">
              {{ item.title }}
            </div> -->
            <Preview v-model:show="showPreDialog" :item="previewItem"/>

			<div class="table">
				<div class="table-content">
					<div v-for="(item, index) in fileList" :key="index" class="table-item" @click="selectItem(item)">
						<div class="table-column" style="flex-shrink: 0;">
							<img src="@/assets/image/resources/Paper.png" class="paper-icon">
						</div>
						<div class="table-column">
							<div class="paper-title" v-html="highlight(item.title)"></div>
							<div class="paper-text"> 作者：{{ item.author }}</div>
							<div class="paper-text"> 
								关键词：
								<span class="paper-text" v-html="highlight(item.keyword)"></span>
							</div>


							<!-- <div class="paper-title">{{ item.title }}</div>
							<div class="paper-text"> 作者：{{ item.author }}</div>
							<div class="paper-text"> 关键词：{{ item.keyword }}</div> -->
						</div>
					</div>
				</div>
				<div class="pagination">
					<el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="totalPage"
					layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
				</div>
			</div>

        </div>
      </div>
    </div>

	<div v-show="showOutside" class="main-paper">
      <div class="content">

		<a-tabs v-model:activeKey="activeTab" @change="tabChange">
			<a-tab-pane key="webPages" tab="网页"></a-tab-pane>
			<a-tab-pane key="images" tab="图片"></a-tab-pane>
			<a-tab-pane key="videos" tab="视频"></a-tab-pane>
		</a-tabs>

		<!-- 网页内容 -->
		<div v-show="activeTab==='webPages'" class="result-title">
			搜索结果 
			<span class="res-text">共有 <span style="color: rgba(63, 140, 255, 1);">{{ webPagesCount }}</span> 个筛选结果</span>
		</div>
		<div v-show="activeTab==='webPages'" class="no-data" v-if="webPagesCount === 0">
			<img src="@/assets/image/resources/nodata.png" alt="暂无资源" />
		</div>
		<div v-show="activeTab==='webPages'" class="show-data" v-else>

			<Preview v-model:show="showPreDialog" :item="previewItem"/>

			<div class="table">
				<div class="table-content">
					<div v-for="(item, index) in webPagesList" :key="index" class="table-item" @click="openLink(item)" 
					style="padding: 5px; border: 1px solid rgba(229, 229, 229, 1);box-shadow: 0px 2px 13px rgba(7, 76, 179, 0.1);">
						<div class="table-column" style="gap:5px">
							<div class="paper-title" v-html="highlight(item.name)"></div>
							<a> {{ item.url }} </a>
							<div class="paper-text"  v-html="highlight(item.snippet)"></div>
							<div class="paper-text"> 收录时间：{{ item.datePublished }} </div>
						</div>
					</div>
				</div>
				<div class="pagination">
					<el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="totalPage"
					layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
				</div>
			</div>
		</div>

		<!-- 图片内容 -->
		<div v-show="activeTab==='images'" class="result-title">
			搜索结果 
			<span class="res-text">共有 <span style="color: rgba(63, 140, 255, 1);">{{ imagesCount }}</span> 个筛选结果</span>
		</div>
		<div v-show="activeTab==='images'" class="no-data" v-if="imagesCount === 0">
			<img src="@/assets/image/resources/nodata.png" alt="暂无资源" />
		</div>
		<div v-show="activeTab==='images'" class="show-data" v-else>

			<Preview v-model="showPreDialog" :item="previewItem"/>

			<div class="table">
				<div class="table-content">
					<div v-for="(item, index) in imagesList" :key="index" class="table-item" @click="selectItem(item)">
						<img :src=item.contentUrl style="width: 100%;">
					</div>
				</div>
				<div class="pagination">
					<el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="totalPage"
					layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
				</div>
			</div>
		</div>

		<!-- 视频内容 -->
		<div v-show="activeTab==='videos'" class="result-title">
			搜索结果 
			<span class="res-text">共有 <span style="color: rgba(63, 140, 255, 1);">{{ videosCount }}</span> 个筛选结果</span>
		</div>
		<div v-show="activeTab==='videos'" class="no-data" v-if="videosCount === 0">
			<img src="@/assets/image/resources/nodata.png" alt="暂无资源" />
		</div>
		<div v-show="activeTab==='videos'" class="show-data" v-else>

			<Preview v-model="showPreDialog" :item="previewItem"/>

			<div class="table">
				<div class="table-content">
					<div v-for="(item, index) in videosList" :key="index" class="table-item" @click="selectItem(item)">
						<div class="table-column" style="flex-shrink: 0;">
							<img src="@/assets/image/resources/Video.png" class="icon">
						</div>
						<div class="table-column">
							<div class="text">{{ item.name }}</div>
						</div>
					</div>
				</div>
				<div class="pagination">
					<el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="totalPage"
					layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
				</div>
			</div>
		</div>

      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { CaretDownOutlined,SearchOutlined } from '@ant-design/icons-vue';
import { getFilterOption,localSearch,webSearch } from '@/services/api/resources'
import { message } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';

//切换站内站外
const switchType = ref('inside');
const showInside = ref(true)
const showOutside = ref(false)
watch(()=>switchType.value, (newVal)=>{
	if(newVal === 'inside'){
		showInside.value = true;
		showOutside.value = false;
	}else{
		showInside.value = false;
		showOutside.value = true;
	}
})

const showResource = ref(true)
const selected = ref('resource')
const keyword = ref('')
const customIcon = h(CaretDownOutlined)
type RangeValue = [Dayjs, Dayjs];
const dateValue = ref<RangeValue>();

const handleChange = () =>{
  console.log(selected.value)
  if (selected.value == 'resource'){
    showResource.value = true
  }else{
    showResource.value = false
  };

  //重置查询总数
  resCount.value = 0;
  //重置列表
  fileList.value = [];
}

interface OptionItem {
  label: string
  value: string
  count: number
}

const options_1 = ref<OptionItem[]>([])
const totalCount_1 = ref(0)

const options_2 = ref<OptionItem[]>([])
const totalCount_2 = ref(0)

onMounted(()=>{
  getFilter()
})

//资源筛选条件获取
const getFilter = async () => {
    try {
      const res = await getFilterOption()
      //获取全部总数
      totalCount_1.value = res.data.file_types.find((item: OptionItem) => item.label === "全部")?.count ?? 0
      //在文件类型中去除'全部'
      options_1.value = res.data.file_types.filter((item:OptionItem) => item.label !== "全部");
      //获取全部总数
      totalCount_2.value = res.data.subjects.find((item: OptionItem) => item.label === "全部")?.count ?? 0
      //在院系专业中去除'全部'
      options_2.value = res.data.subjects.filter((item:OptionItem) => item.label !== "全部");
      message.success('筛选条件获取完成');
    } catch (error) {
      message.error('筛选条件获取失败');
      console.error('筛选条件获取失败:', error);
    }
}

const resCount = ref(0)
const fileList = ref<any[]>([])

const webPagesCount = ref(0)
const imagesCount = ref(0)
const videosCount = ref(0)

const webPagesList = ref<any[]>([])
const imagesList = ref<any[]>([])
const videosList = ref<any[]>([])

const onSearch = async() => {
	if (switchType.value === 'inside'){
		//站内搜索
		try {
			//分页器页数设置
			if(selected.value === 'resource'){
				pageSize.value = 20;
			}else if(selected.value === 'paper'){
				pageSize.value = 14;
			}

			const params = {
				tag: selected.value,
				page: currentPage.value,
				page_size: pageSize.value,
				query: keyword.value,
				file_type: JSON.stringify([...checkedList_1.value]),
				subject: JSON.stringify([...checkedList_2.value]),
				start_date: dateValue.value?.[0],
				end_date: dateValue.value?.[1],
			}

			// console.log(params.file_type,'params');
			const res = await localSearch(params);
			// console.log(res.data,"resresresres");
			resCount.value = res.data.count;
			totalPage.value =  resCount.value;
			fileList.value = res.data.results;
			// console.log(fileList.value);

			message.success('资源检索完成');
		} catch (error) {
			message.error('资源检索失败');
			console.error('资源检索失败:', error);
		}
	}
	else{
		//站外搜索
		try {
			const params = {
				query: keyword.value,     //用户的搜索词
				freshness: "noLimit",                //搜索指定时间范围内的网页
				summary: true,                       //是否显示文本摘要
				count: 50,                           //返回结果的条数,选择范围1-50
				// "include":"kns.cnki.net"          //指定搜索的网站范围,多个域名使用|或,分隔
				page: currentPage.value,
				page_size: pageSize.value,
			}
			
			const res = await webSearch(params);
			console.log(res.data,"resresresres");

			webPagesCount.value = res.data.webPages.total
			imagesCount.value = res.data.images.total
			videosCount.value = resData.data.videos.total

			//分页器页数赋值
			if(activeTab.value === 'webPages'){
				totalPage.value =  webPagesCount.value;
			}else if(activeTab.value === 'images'){
				totalPage.value =  imagesCount.value;
			}else if(activeTab.value === 'videos'){
				totalPage.value =  videosCount.value;
			}

			webPagesList.value = res.data.webPages.data
			imagesList.value = res.data.images.data
			videosList.value = res.data.videos.data

			message.success('资源检索完成');
		} catch (error) {
			message.error('资源检索失败');
			console.error('资源检索失败:', error);
		}
	}

}


//清空筛选
const clearFilters = () => {
  checkedList_1.value = []
  checkedList_2.value = []
}

//筛选
const applyFilters = () => {
  console.log('筛选文件类型:', checkedList_1.value)
  console.log('筛选院系专业:', checkedList_2.value)
  console.log('筛选开始时间:', dateValue.value?.[0])
}

const showFile = ref(true)
const handleShowFile = () =>{
  showFile.value = !showFile.value;
}

const showDepartment = ref(true)
const handleShowDepartment= () =>{
  showDepartment.value = !showDepartment.value;
}

const showDate = ref(true)
const handleShowDate = () =>{
  showDate.value = !showDate.value;
}


//文件类型
// const options_1: OptionItem[] = [
//   { label: '文档（word、pdf）', value: 'doc', count: 20 },
//   { label: 'PPT', value: 'ppt', count: 2 },
//   { label: '视频、图片', value: 'media', count: 10 }
// ]

const checkedList_1 = ref<string[]>([])

const checkAll_1 = computed(() => checkedList_1.value.length === options_1.value.length && options_1.value.length != 0)

const indeterminate_1 = computed(
  () => checkedList_1.value.length > 0 && checkedList_1.value.length < options_1.value.length
)
const onGroupChange_1 = (list: any) => {
  checkedList_1.value = list
}

const onCheckAllChange_1 = (e: any) => {
  checkedList_1.value = e.target.checked ? options_1.value.map(item => item.value) : []
}


// const options_2: OptionItem[] = [
//   { label: '基础课', value: '基础课', count: 20 },
//   { label: '智能制造系', value: '智能制造系', count: 2 },
//   { label: '马克思主义学院', value: '马克思主义学院', count: 10 },
//   { label: '公共教育部', value: '公共教育部', count: 10 },
//   { label: '经济管理系', value: '经济管理系', count: 10 },
//   { label: '现代农业系', value: '现代农业系', count: 10 },
//   { label: '现代工程系', value: '现代工程系', count: 10 },
//   { label: '人文艺术系', value: '人文艺术系', count: 10 },
//   { label: '生态环保系', value: '生态环保系', count: 10 },
//   { label: '汽车工程系', value: '汽车工程系', count: 10 },
//   { label: '西临梦想家学院（筹）', value: '西临梦想家学院（筹）', count: 10 },
// ]

const checkedList_2 = ref<string[]>([])

const checkAll_2 = computed(() => checkedList_2.value.length === options_2.value.length && options_2.value.length != 0)

const indeterminate_2 = computed(
  () => checkedList_2.value.length > 0 && checkedList_2.value.length < options_2.value.length
)

const onGroupChange_2 = (list: any) => {
  checkedList_2.value = list
}

const onCheckAllChange_2 = (e: any) => {
  checkedList_2.value = e.target.checked ? options_2.value.map(item => item.value) : []
}

const showPreDialog = ref(false)
const previewItem = ref();

function selectItem(item:any) {
  showPreDialog.value = true;
  previewItem.value = item
}

const openLink = (item:any) => {
  window.open(item.url, '_blank'); // '_blank' 用于新标签页打开
}

function highlight(text: string) {
  if (!keyword.value) return text;
  const regex = new RegExp(`(${keyword.value})`, 'gi');
  return text.replace(regex, `<span style="color: red;">${keyword.value}</span>`);
}

const totalPage = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, totalPage.value, onSearch);

const activeTab = ref('webPages')
const tabChange = () => {
	if(activeTab.value === 'webPages'){
		totalPage.value =  webPagesCount.value;
	}else if(activeTab.value === 'images'){
		totalPage.value =  imagesCount.value;
	}else if(activeTab.value === 'videos'){
		totalPage.value =  videosCount.value;
	}
}

const resTest = {
	"code": 200,
	"message": "success",
	"data": {
		"count": 13,
		"next": "http://192.168.110.57:18000/api/v1/retrieve/synthetical_search/?end_date=2025-06-30&file_type=%5B%22document%22%2C%22ppt%22%2C%22media%22%5D&page=2&page_size=10&query=%E6%B5%8B%E8%AF%95&start_date=2025-06-26&tag=resource",
		"previous": null,
		"results": [
			{
				"id": 13,
				"title": "PPT模板（彩色风格）  - .pptx",
				"file": "http://192.168.110.57:18000/media/retrieve_resources_file/1751273061_PPT%E6%A8%A1%E6%9D%BF%E5%BD%A9%E8%89%B2%E9%A3%8E%E6%A0%BC__-_.pptx",
				"author": "暂无",
				"keyword": "测试图片3",
				"file_type": "ppt",
				"file_type_display": "PPT",
				"tag": "resource",
				"tag_display": "资源",
				"subjects": [
					{
						"id": 1,
						"name": "基础课"
					}
				],
				"created_at": "2025-06-30T16:44:21.974290+08:00",
				"updated_at": "2025-06-30T16:44:21.974290+08:00",
				"user": null,
				"plays_number": null
			},
			{
				"id": 12,
				"title": "提交采购.pdf",
				"file": "http://192.168.110.57:18000/media/retrieve_resources_file/1751267333_%E6%8F%90%E4%BA%A4%E9%87%87%E8%B4%AD.pdf",
				"author": "暂无",
				"keyword": "测试图片2",
				"file_type": "pdf",
				"file_type_display": "PDF文档",
				"tag": "resource",
				"tag_display": "资源",
				"subjects": [
					{
						"id": 1,
						"name": "基础课"
					}
				],
				"created_at": "2025-06-30T15:08:53.402937+08:00",
				"updated_at": "2025-06-30T15:08:53.402937+08:00",
				"user": null,
				"plays_number": null
			},
			{
				"id": 11,
				"title": "物理教案.docx",
				"file": "http://192.168.110.57:18000/media/retrieve_resources_file/1751266162_%E7%89%A9%E7%90%86%E6%95%99%E6%A1%88.docx",
				"author": "暂无",
				"keyword": "测试图片1",
				"file_type": "word",
				"file_type_display": "word文档",
				"tag": "resource",
				"tag_display": "资源",
				"subjects": [
					{
						"id": 1,
						"name": "基础课"
					}
				],
				"created_at": "2025-06-30T14:49:22.774789+08:00",
				"updated_at": "2025-06-30T14:49:22.774789+08:00",
				"user": null,
				"plays_number": null
			},
			{
				"id": 10,
				"title": "物理教案.docx",
				"file": "http://192.168.110.57:18000/media/retrieve_resources_file/1751266030_%E7%89%A9%E7%90%86%E6%95%99%E6%A1%88.docx",
				"author": "暂无",
				"keyword": "测试图片1",
				"file_type": "word",
				"file_type_display": "word文档",
				"tag": "resource",
				"tag_display": "资源",
				"subjects": [
					{
						"id": 1,
						"name": "基础课"
					}
				],
				"created_at": "2025-06-30T14:47:10.581783+08:00",
				"updated_at": "2025-06-30T14:47:10.581783+08:00",
				"user": null,
				"plays_number": null
			},
		]
	}
}

const resData = {
	"code": 200,
	"message": "success",
	"data": {
		"webPages": {
			"data": [
				{
					"id": "https://api.bochaai.com/v1/#WebPages.0",
					"name": "人工智能资讯_电子发烧友网",
					"url": "https://www.elecfans.com/tags/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD/news/441/",
					"displayUrl": "https://www.elecfans.com/tags/人工智能/news/441/",
					"snippet": "人工智能 +关注 1753 人关注 人工智能(Artificial Intelligence),英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学",
					"summary": "人工智能 +关注 1753 人关注 人工智能(Artificial Intelligence),英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。 文章: 44270 个 浏览: 240251 次 帖子: 1065 个 为了在熟悉的开放平台中提供高级人工智能和机器学习所需的性能,BeagleBoard.org 推出了BeagleBone® AI-64 开放式硬件单板计算... 由于计算机架构的革命性发展以及人工智能和机器学习应用的突破性进展,嵌入式系统技术正在经历一个转型时期。 为应对数字时代的全新挑战和更高的技术需求,英特尔旨在通过包括无所不在的计算、无处不在的连接、从云到边缘的基础设施、人工智能、传感和感知在内的“五大超级技... 11.16日,“OFweek 2022(第七届)人工智能产业大会”在深圳大中华喜来登酒店举办。本届展会以“智存高远·融创未来”为主题,集中展示人工智能与... 计算光学成像,顾名思义,是把“计算”融入到光学图像形成过程中任何一个或者多个环节的一类新型的成像技术或系统。光学图像的形成与场景/物体的照明模式、系统的... 11月16日由维科网主办的“第七届人工智能行业年度评选颁奖典礼”在深圳大中华喜来登酒店隆重举行。研华科技人工智能推理系统AIR-020,凭借高算力、高扩... 2022年11月15日,第二十四届中国国际高新技术成果交易会(简称“高交会”)在深圳开幕。作为高交会的重要组成部分,“中国高新技术论坛”邀请了来自国... 随着人工智能、物联网、大数据、云计算等新一代信息技术的突破,以DCS、PLC、SCADA为代表的数字化控制系统也逐步向智能控制系统方向发展。 AIGC 即AI Generated Content,利用人工智能技术来生成内容,它被认为是继PGC、UGC之后的新型内容",
					"siteName": "电子发烧友网",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://www.elecfans.com/tags/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD/news/441/",
					"datePublished": "2022-11-17T13:04:12+08:00",
					"dateLastCrawled": "2022-11-17T13:04:12Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.1",
					"name": "人工智能",
					"url": "https://www.elecfans.com/tags/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD/news/14/",
					"displayUrl": "https://www.elecfans.com/tags/人工智能/news/14/",
					"snippet": "人工智能(Artificial Intelligence),英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。\n人工智能资讯\n老年人行为识别:A",
					"summary": "人工智能(Artificial Intelligence),英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。\n人工智能资讯\n老年人行为识别:AI为下一代老年人带来希望\n​忙碌的生活让我们容易忽视父母的情感需求。当我们回忆他们为我们付出的一切时,是否意识到时间流逝得如此之快?最近,我在参与一个老年人行为识别挑战时发现,许...\n2025-01-20  标签: 机器人 AI 人工智能 127 0\nAI大模型师资培训:人工智能虚拟仿真系统助力创新教学与非凡就业\n2025年1月13日-1月17日,由华清远见科技教育集团举办的第31届全国高校AI大模型寒假师资班,圆满结束。此次师资班,得到了众多高校教师热烈响应,共...\n2025-01-20  标签: 算法 AI 人工智能 462 0\n2025,芯片行业的重塑之年\n本文由半导体产业纵横(ID:ICVIEWS)编译自SemiconductorEngineering这将是令人难以置信的创新之年,由人工智能驱动,并为人工...\n2025-01-20  标签: 芯片 半导体 人工智能 171 0\n机器人相关动态汇总\n埃夫特发布公告称,其全资孙公司Autorobot近日收到客户关于汽车产线建设的采购订单,折合人民币约1.09亿元,订单合约期为2025-2028年。\n2025-01-20  标签: 机器人 人工智能 人形机器人 341 0\n生成式AI推理技术、市场与未来\nOpenAI o1、QwQ-32B-Preview、DeepSeek R1-Lite-Preview的相继发布,预示着生成式AI研究正从预训练转向推理(...\n2025-01-20  标签: AI 人工智能 463 0\n一文解析2025年后人工智能的发展前景\n本文探讨了人工智能(AI)在不久的将来及远期预期的进步、应用和",
					"siteName": "电子发烧友网",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://www.elecfans.com/tags/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD/news/14/",
					"datePublished": "2025-01-20T08:00:00+08:00",
					"dateLastCrawled": "2025-01-20T08:00:00Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.2",
					"name": "人工智能(Artificial Intelligence,AI)是一种模拟人类智能的技术_artificial intelligence 模仿人的-CSDN博客",
					"url": "https://blog.csdn.net/y632481222/article/details/135174015",
					"displayUrl": "https://blog.csdn.net/y632481222/article/details/135174015",
					"snippet": "在现代科技中,人工智能的应用非常广泛,包括但不限于以下几个方面: 语音和图像识别:人工智能可以通过深度学习算法识别和理解语音和图像,从而实现自然语言处理、人脸识别和图像分类等任务。 自动驾驶:人工智能",
					"summary": "在现代科技中,人工智能的应用非常广泛,包括但不限于以下几个方面: 语音和图像识别:人工智能可以通过深度学习算法识别和理解语音和图像,从而实现自然语言处理、人脸识别和图像分类等任务。 自动驾驶:人工智能在自动驾驶领域发挥重要作用,通过感知、决策和控制等技术,使汽车能够自主行驶,提高交通安全性和效率。 金融领域:人工智能可以通过大数据分析和机器学习算法,实现风险评估、投资决策和欺诈检测等任务,提高金融机构的效益和安全性。 医疗健康:人工智能可以根据医学图像和数据,辅助医生进行疾病诊断和治疗方案制定,提高医疗精确度和效率。 智能助理:人工智能可以为用户提供个性化的信息推荐和服务,例如智能音箱、智能手机助手等,提升用户体验和生活品质。 未来发展趋势方面,人工智能有以下几个重要方向: 深度学习和神经网络:深度学习技术是目前人工智能最热门的领域,未来将继续深入研究和应用,提高算法的效率和准确性。 自主学习和迁移学习:人工智能在学习过程中通常需要大量的标注数据,未来研究将着重于提高机器的自主学习能力和知识迁移能力。 机器人技术:人工智能与机器人技术的融合将会带来更智能化和灵活的机器人,应用领域将进一步扩展。 联邦学习和隐私保护:随着数据的积累和敏感性增加,人工智能领域将进一步探索在保护用户隐私的前提下,实现数据共享和模型训练。 跨学科合作:人工智能的发展需要多学科的交叉合作,未来将与计算机科学、数学、心理学等领域深入合作,共同推动人工智能的研究和应用。 总的来说,人工智能在现代科技中的应用已经非常广泛,并且未来还有更多的发展潜力。无论是从技术的创新还是应用的拓展上,人工智能都将在科技领域发挥重要的作用,为人类带来更多的便利和进步。",
					"siteName": "CSDN",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://blog.csdn.net/y632481222/article/details/135174015",
					"datePublished": "2024-04-18T14:31:16+08:00",
					"dateLastCrawled": "2024-04-18T14:31:16Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.3",
					"name": "a人工智能_人工智能 - 酷盾",
					"url": "https://www.kdun.com/ask/690741.html",
					"displayUrl": "https://www.kdun.com/ask/690741.html",
					"snippet": "a人工智能_人工智能 人工智能(AI)是计算机科学的一个分支,它试图理解和构建智能系统,特别是那些能够执行通常需要人类智能的任务的系统。AI的研究领域包括机器学习、自然语言处理、计算机视觉和机器人技术",
					"summary": "a人工智能_人工智能 人工智能(AI)是计算机科学的一个分支,它试图理解和构建智能系统,特别是那些能够执行通常需要人类智能的任务的系统。AI的研究领域包括机器学习、自然语言处理、计算机视觉和机器人技术等。 人工智能(a人工智能_人工智能) (图片来源网络,侵删) 人工智能(artificial intelligence, ai)是指由人制造出来的系统所表现出来的智能,通过学习、理解、推理、感知、语言识别等过程,实现机器自动执行工作以及解决问题的能力。 历史发展 年份 事件 1956 达特茅斯会议,正式提出“人工智能”这一概念 1997 ibms深蓝击败国际象棋世界冠军卡斯帕罗夫 2011 watson在美国智力竞赛节目《危险边缘》中战胜人类选手 2016 alphago战胜围棋世界冠军李世石 主要研究领域 1、机器学习:通过算法让计算机从大量数据中学习规律和知识。 2、 自然语言处理 :使计算机能够理解、解释和生成人类语言。 3、 计算机视觉 :使计算机能够理解和解析图像和视频内容。 4、 语音识别 :将人类的语音转换成文字或者计算机命令。 (图片来源网络,侵删) 5、 机器人技术 :集成多种ai技术以实现机器人的自主行动和决策。 应用领域 医疗健康 :辅助诊断、药物研发、健康管理等。 金融服务 :风险管理、算法交易、客户服务等。 智能制造 :自动化生产、质量检测、设备维护等。 交通运输 :自动驾驶、交通流量分析、智能调度等。 消费电子 :智能个人助理、推荐系统、智能家居控制等。 (图片来源网络,侵删) 未来趋势 1、 普及化 :ai技术将更加普及,融入日常生活的方方面面。 2、 伦理与法规 :随着ai的发展,相关的伦理和法律问题将越来越受到关注。 3、 可解释性 :提高ai决策过程的透明度和可解释性将成为研究重点。 4、 跨学科融合 :ai将与更多学科如生物学、心理学等领",
					"siteName": "酷盾",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://www.kdun.com/ask/690741.html",
					"datePublished": "2024-06-14T23:48:00+08:00",
					"dateLastCrawled": "2024-06-14T23:48:00Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.4",
					"name": "人工智能学习网站-CSDN博客",
					"url": "https://blog.csdn.net/tblmars/article/details/131587654",
					"displayUrl": "https://blog.csdn.net/tblmars/article/details/131587654",
					"snippet": "版权声明:本文为博主原创文章,遵循CC 4.0 BY-SA版权协议,转载请附上原文出处链接和本声明。 版权         人工智能(AI)是一种模拟人类智能的科技,旨在使机器能够执行类似于人类思维和",
					"summary": "版权声明:本文为博主原创文章,遵循CC 4.0 BY-SA版权协议,转载请附上原文出处链接和本声明。 版权         人工智能(AI)是一种模拟人类智能的科技,旨在使机器能够执行类似于人类思维和决策过程的任务。它利用计算机系统和算法来处理、分析和解释大量的数据,从而识别模式、做出预测并自动调整行为。         AI的发展涉及多个领域,其中最重要的是机器学习和深度学习。通过机器学习,机器可以从数据中学习,并根据其经验提高性能。深度学习则是一种机器学习的子领域,通过构建人工神经网络来模仿人脑的结构和功能。这种网络可以处理复杂的非线性关系,并在图像识别、语音识别、自然语言处理等任务上取得惊人的成果。         AI的应用广泛,几乎涵盖了所有领域。在医疗保健方面,AI可以帮助医生进行诊断和治疗决策,加速药物研发和创新,改善病人的护理和监护。在金融领域,AI被应用于风险评估、欺诈检测和投资建议等方面,提高了效率和准确性。在交通领域,AI技术用于自动驾驶汽车、交通流量优化和智能交通管理,提高了安全性和可持续性。在制造业中,AI可以优化生产过程、预测设备故障并进行智能调度。在娱乐领域,AI带来了虚拟现实、增强现实和智能游戏等新的体验。         然而,AI也面临一些挑战和争议。其中之一是隐私问题。AI系统需要大量的数据来学习和作出决策,但这可能涉及搜集和分析个人的敏感信息。另一个问题是道德和伦理问题。例如,自动驾驶汽车在面临道德困境时如何做出决策是一个复杂的问题。此外,AI的算法和模型可能存在偏见和歧视,需要更多的努力来解决这些问题。         尽管有挑战,但AI持续推动着科技的创新和进步,并为我们的社会和生活带来了巨大的影响。它提供了新的工具和解决方案,改善了我们的生产效率、决策能力和生活质量。然而,我们也需要谨慎使用和监管AI技术,确保其安全、可靠和公平",
					"siteName": "CSDN",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://blog.csdn.net/tblmars/article/details/131587654",
					"datePublished": "2023-07-07T00:20:13+08:00",
					"dateLastCrawled": "2023-07-07T00:20:13Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.5",
					"name": "人工智能-指股网",
					"url": "https://www.zhiguf.com/focusnews_detail/1260949",
					"displayUrl": "https://www.zhiguf.com/focusnews_detail/1260949",
					"snippet": "人工智能(AI)是一种模拟人类智能的计算机系统。它利用算法和大数据分析模仿人类思维,执行任务如语言识别、图像识别和决策制定。AI应用广泛,覆盖医疗、金融、制造业等领域。随着机器学习和深度学习技术的发展",
					"summary": "人工智能(AI)是一种模拟人类智能的计算机系统。它利用算法和大数据分析模仿人类思维,执行任务如语言识别、图像识别和决策制定。AI应用广泛,覆盖医疗、金融、制造业等领域。随着机器学习和深度学习技术的发展,AI正成为许多行业的关键驱动力,推动社会创新。 人工智能概述 人工智能(Artificial Intelligence, AI)是指由人制造出来的机器或软件体现出的智能,它能够通过学习、推理、适应、甚至理解语言,执行各种复杂的计算和任务。随着技术的发展,人工智能已从科幻小说的概念转变为现实生活中的重要组成部分,广泛应用于医疗、教育、金融、制造业、自动驾驶等领域。 历史与发展 人工智能的概念最早可以追溯到20世纪50年代,当时的科学家们开始探索制造能模拟人类智能行为的机器。自那时起,人工智能经历了几个重要的发展阶段,包括符号主义学习、机器学习,以及当前极受关注的深度学习技术。每一阶段的技术进步都极大推动了人工智能领域的发展,使其能解决更加复杂的问题。 核心技术 机器学习是人工智能的一个核心分支,它使计算机能够通过数据学习并做出决策或预测,而无需进行明确的程序编码。机器学习主要包括监督学习、非监督学习和强化学习等类型。 深度 学习 深度学习是机器学习中的一种方法,它模拟人类大脑的神经网络结构,通过多层次的神经网络处理和分析数据。深度学习在图像识别、语音识别和自然语言处理等领域取得了革命性的进展。 自然语言处理 自然语言处理(NLP)是人工智能的另一个重要分支,它让计算机能够理解、解释和生成人类语言。NLP技术的应用包括机器翻译、情感分析、聊天机器人等。 应用领域 医疗健康: 在医疗健康领域,人工智能技术通过分析大量的医疗数据帮助医生诊断疾病、制定治疗计划,甚至在药物研发中发挥作用。 金融科技: 人工智能在金融科技领域的应用极大提高了金融服务的效率和安全性,包括算法交易、风险管理、",
					"siteName": "指股财经网",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://www.zhiguf.com/focusnews_detail/1260949",
					"datePublished": "2024-07-06T16:16:00+08:00",
					"dateLastCrawled": "2024-07-06T16:16:00Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.6",
					"name": "人工智能",
					"url": "https://caifuhao.eastmoney.com/news/20250207012611838585080",
					"displayUrl": "https://caifuhao.eastmoney.com/news/20250207012611838585080",
					"snippet": "人工智能(Artificial Intelligence,简称AI)是指通过计算机系统模拟人类智能的能力,包括学习、推理、问题解决、感知、语言理解等。AI技术广泛应用于各个领域,如自动驾驶、医疗诊断、",
					"summary": "人工智能(Artificial Intelligence,简称AI)是指通过计算机系统模拟人类智能的能力,包括学习、推理、问题解决、感知、语言理解等。AI技术广泛应用于各个领域,如自动驾驶、医疗诊断、金融分析、语音识别、图像处理等。",
					"siteName": "财富号",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://caifuhao.eastmoney.com/news/20250207012611838585080",
					"datePublished": "2025-02-07T09:26:00+08:00",
					"dateLastCrawled": "2025-02-07T09:26:00Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.7",
					"name": "人工智能技术小报(2页)-原创力文档",
					"url": "https://max.book118.com/html/2021/1104/8116135123004031.shtm",
					"displayUrl": "https://max.book118.com/html/2021/1104/8116135123004031.shtm",
					"snippet": "涉及学科哲学和认知科学,数学. 神经生理学,心理学, 计算机科学,信息论, 控制论,不定性论,仿生学。应用领域智能控制,机器人学,语言和图像理解,遗传编程。人工智能 (Artificial Intel",
					"summary": "涉及学科哲学和认知科学,数学. 神经生理学,心理学, 计算机科学,信息论, 控制论,不定性论,仿生学。应用领域智能控制,机器人学,语言和图像理解,遗传编程。人工智能 (Artificial Intelligen,ce英) 文缩写为 AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。 人工智能是计算机科学的一个分支,它企图了解智能的实质,并生产 出一种新的能以人类智能相似的方式做出反应的智能机器,该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。 “人工智能 ”一词最初是在 1956研究范畴实际应用机器视觉:指纹识别,人脸识别, 视网膜识别,虹膜 识别,掌纹识别, 专家系统,智能搜索,定理证明,博弈,自动程序设计, 还有航天应用等。年Dartmouth 学会上提出的。从那以后 ,研究者们发展了众多理论和原理 ,人工智能的概念也随之扩展。 人工智能是一门极富挑战性的科学,从事这项工作的人必须懂得计算 机知识,心理学和哲学。 人工智能是包括十分广泛的科学, 它由不同的领域组成,如机器学习,计算机视觉等等,总 的说来,人工智能研究的一个主要目标是使机器能够胜任 一些通常需要人类智能才能完成的复杂工作。 目前能够用来研究人工智能的主要物质手段以及能够实现人工智能技术的机器就是计算机,人工智能的发展历史是和计算机科学与技术的发展史联系在一起的。自然语言处理知识表现智能搜索,推理,规划,机器学习,知识获取,组合调度问题,感知问题,模式识别,逻辑程序设计,软计算,不精确和不确定的管理,人工生命,神经网络,复杂系统,遗传算法学科范畴人工智能是一门边沿学科,属于自然科学和社会科学的交叉。经典的人工智能成果人机对弈1996 年2月10 ~17 日, Garry Kasparov 以4:2战胜“深蓝”(Deep Blue )。199",
					"siteName": "原创力文档",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://max.book118.com/html/2021/1104/8116135123004031.shtm",
					"datePublished": "2021-11-05T03:52:49+08:00",
					"dateLastCrawled": "2021-11-05T03:52:49Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.8",
					"name": "人工智能",
					"url": "https://m.baike.com/wikiid/7201379635673169975",
					"displayUrl": "https://m.baike.com/wikiid/7201379635673169975",
					"snippet": "人工智能(英文名:Artificial Intelligence,英文缩写:AI)是一门综合了 计算机科学 、控制论、信息论、神经生理学、心理学、 语言学 、哲学等多种学科互相渗透而发展起来的一门交叉",
					"summary": "人工智能(英文名:Artificial Intelligence,英文缩写:AI)是一门综合了 计算机科学 、控制论、信息论、神经生理学、心理学、 语言学 、哲学等多种学科互相渗透而发展起来的一门交叉学科 [ 6 ] ,它通过计算机去模拟人的思维和行为,其核心是机器学习算法。人工智能这一概念在1956年的 达特茅斯会议 上正式提出 [ 1 ] [ 7 ] ,人工智能是指通过分析其环境而具有一定程度的自主性行动,以实现特定目标而显示智能行为的系统 [ 8 ] 。\n人工智能包括计算力的突破、数据洪流和算法创新三大关键技术 [ 1 ] ,其被称为世界三大尖端技术之一,同时也被认为是21世纪三大尖端技术之一, [ 9 ] 其发展的主流形态采取深度学习算法、大模型、 大数据 的方式 [ 10 ] 。\n人工智能技术体系包括机器学习、自然语言处理技术、图像处理技术、 人机交互 技术 [ 11 ] 。人工智能已在大数据分析、 自动驾驶、 智慧金融和智能机器人等多个领域取得了举世瞩目的成果, 并形成了多元化的发展方向 [ 12 ] ,另外人工智能可替代部分传统劳动力,产生劳动挤出效应,在提高社会生产效率的同时也为社会创造了全新的工作岗位 [ 13 ] 。\n发展历程\n李永乐老师介绍人工智能的发展历史\n人类探索智能的道路是十分漫长的,最早可以追溯1900年以前的早期传说、文学和影视作品中的高级智慧 [14] ,20世纪, 乔治·布尔 的《思维规律的研究》、 弗雷格 的《 概念文字 》、 伯特兰·罗素 和其老师 怀特海 的《 数学原理 》这些著作在数理逻辑研究上有了极大的突破,麦卡洛克和皮茨于1943年提出的人工神经网络的概念以及后续构建的人工神经元的MP模型开创了人工神经网络研究的时代,赫伯学习规则以及1946年世界上第一台数字式电子 计算机 的出现对人工智能的发展也起到很大帮助 [15] [1",
					"siteName": "抖音百科",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://m.baike.com/wikiid/7201379635673169975",
					"datePublished": "2023-02-20T09:21:48+08:00",
					"dateLastCrawled": "2023-02-20T09:21:48Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				},
				{
					"id": "https://api.bochaai.com/v1/#WebPages.9",
					"name": "人工智能-速石科技",
					"url": "https://fastonetech.com/newszblog/post/38150.html",
					"displayUrl": "https://fastonetech.com/newszblog/post/38150.html",
					"snippet": "人工智能 人工智能(Artificial Intelligence)是研究和开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。它是计算机科学、神经科学、心理学、语言学、机",
					"summary": "人工智能 人工智能(Artificial Intelligence)是研究和开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。它是计算机科学、神经科学、心理学、语言学、机器学等多学科交叉的新兴领域。人工智能的研究包括机器人、语言识别、图像识别、自然语言处理、语义分析、智能推理和机器学习等。",
					"siteName": "速石科技官网",
					"siteIcon": "https://th.bochaai.com/favicon?domain_url=https://fastonetech.com/newszblog/post/38150.html",
					"datePublished": "2023-02-26T20:05:36+08:00",
					"dateLastCrawled": "2023-02-26T20:05:36Z",
					"cachedPageUrl": null,
					"language": null,
					"isFamilyFriendly": null,
					"isNavigational": null
				}
			],
			"total": 47,
			"total_pages": 5,
			"page": 1
		},
		"images": {
			"data": [
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://skin.elecfans.com/tags/images/u287.png",
					"datePublished": null,
					"contentUrl": "https://skin.elecfans.com/tags/images/u287.png",
					"hostPageUrl": "https://www.elecfans.com/tags/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD/news/441/",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://www.elecfans.com/tags/人工智能/news/441/",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://wp-com.uploads.cn/wp-content/uploads/2024/06/618bb2735ef540c5fbaf2b8710605ecd.jpg",
					"datePublished": null,
					"contentUrl": "https://wp-com.uploads.cn/wp-content/uploads/2024/06/618bb2735ef540c5fbaf2b8710605ecd.jpg",
					"hostPageUrl": "https://www.kdun.com/ask/690741.html",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://www.kdun.com/ask/690741.html",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://fastonetech.com/zb_users/cache/ly_autoimg/m/MzgxNTA.jpg",
					"datePublished": null,
					"contentUrl": "https://fastonetech.com/zb_users/cache/ly_autoimg/m/MzgxNTA.jpg",
					"hostPageUrl": "https://fastonetech.com/newszblog/post/38150.html",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://fastonetech.com/newszblog/post/38150.html",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://imgkepu.gmw.cn/attachement/jpg/site2/20220311/94c69122e51c23955cff3a.jpg",
					"datePublished": null,
					"contentUrl": "https://imgkepu.gmw.cn/attachement/jpg/site2/20220311/94c69122e51c23955cff3a.jpg",
					"hostPageUrl": "https://kepu.gmw.cn/2022-03/11/content_35580982.htm",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://kepu.gmw.cn/2022-03/11/content_35580982.htm",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://imgkepu.gmw.cn/attachement/jpg/site2/20220311/94c69122e51c23955d0d3c.jpg",
					"datePublished": null,
					"contentUrl": "https://imgkepu.gmw.cn/attachement/jpg/site2/20220311/94c69122e51c23955d0d3c.jpg",
					"hostPageUrl": "https://kepu.gmw.cn/2022-03/11/content_35580982.htm",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://kepu.gmw.cn/2022-03/11/content_35580982.htm",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://imgkepu.gmw.cn/attachement/jpg/site2/20220311/94c69122e51c23955d4540.jpg",
					"datePublished": null,
					"contentUrl": "https://imgkepu.gmw.cn/attachement/jpg/site2/20220311/94c69122e51c23955d4540.jpg",
					"hostPageUrl": "https://kepu.gmw.cn/2022-03/11/content_35580982.htm",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://kepu.gmw.cn/2022-03/11/content_35580982.htm",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://imgdigital.gmw.cn/attachement/png/site2/20230516/309c23cbaa9425cd255e01.png",
					"datePublished": null,
					"contentUrl": "https://imgdigital.gmw.cn/attachement/png/site2/20230516/309c23cbaa9425cd255e01.png",
					"hostPageUrl": "https://digital.gmw.cn/2023-05/16/content_36563287.htm",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://digital.gmw.cn/2023-05/16/content_36563287.htm",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://imgdigital.gmw.cn/attachement/png/site2/20230516/309c23cbaa9425cd256402.png",
					"datePublished": null,
					"contentUrl": "https://imgdigital.gmw.cn/attachement/png/site2/20230516/309c23cbaa9425cd256402.png",
					"hostPageUrl": "https://digital.gmw.cn/2023-05/16/content_36563287.htm",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://digital.gmw.cn/2023-05/16/content_36563287.htm",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://imgdigital.gmw.cn/attachement/png/site2/20230516/309c23cbaa9425cd257003.png",
					"datePublished": null,
					"contentUrl": "https://imgdigital.gmw.cn/attachement/png/site2/20230516/309c23cbaa9425cd257003.png",
					"hostPageUrl": "https://digital.gmw.cn/2023-05/16/content_36563287.htm",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://digital.gmw.cn/2023-05/16/content_36563287.htm",
					"width": 0,
					"height": 0,
					"thumbnail": null
				},
				{
					"webSearchUrl": null,
					"name": null,
					"thumbnailUrl": "https://nimg.ws.126.net/?url=http%3A%2F%2Fdingyue.ws.126.net%2F2025%2F0412%2F36410d5bj00sukrw500hkd000hs00a0g.jpg&thumbnail=660x2147483647&quality=80&type=jpg",
					"datePublished": null,
					"contentUrl": "https://nimg.ws.126.net/?url=http%3A%2F%2Fdingyue.ws.126.net%2F2025%2F0412%2F36410d5bj00sukrw500hkd000hs00a0g.jpg&thumbnail=660x2147483647&quality=80&type=jpg",
					"hostPageUrl": "https://www.163.com/dy/article/JSU6QRML055040N3.html",
					"contentSize": null,
					"encodingFormat": null,
					"hostPageDisplayUrl": "https://www.163.com/dy/article/JSU6QRML055040N3.html",
					"width": 0,
					"height": 0,
					"thumbnail": null
				}
			],
			"total": 20,
			"total_pages": 2,
			"page": 1
		},
		"videos": {
			"data": [],
			"total": 0,
			"total_pages": 0,
			"page": 1
		}
	}
}

</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  min-width: 800px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 110px 220px 70px 220px;
  background: url(@/assets/image/resources/bg.png) no-repeat center;
  background-size: cover;


  .top-section {
    text-align: center;
    margin-bottom: 30px;

    .page-title {
      font-size: 24px;
      font-weight: 700;
      letter-spacing: 0px;
      line-height: 24px;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 20px;
    }

	.switch-but{
	  width:600px;
      height: 40px;
	  margin: 0 auto; /* 水平居中 */
	  display: flex;
      align-items: center;
      padding: 0 20px;
      max-width: 600px;
	  gap:5px;

	}

    .search-bar {
	  width:600px;
      height: 50px;
	  margin: 0 auto; /* 水平居中 */
      display: flex;
      align-items: center;
      background-color: #f5f9fc;
      border-radius: 999px;
      padding: 0 40px;
      max-width: 600px;
      border: 1px solid rgba(255, 255, 255, 1);
      box-shadow: 0px 6px 58px  rgba(196, 203, 214, 0.1);

      .search-select {
        width: 55px;

        :deep(.ant-select-selector) {
          background: transparent;
          border: none !important;
          box-shadow: none !important;
          padding: 0;
        }

        :deep(.ant-select-selection-item) {
          font-size: 15px;
          text-align: left;
          padding:0;
        }

        :deep(.ant-select-arrow) {
          color: #666;
        }
      }

      .search-input {
        flex: 1;
        background: transparent;

        :deep(.ant-input) {
          background: transparent;
          border: none;
          box-shadow: none;
        }
      }

      .search-icon {
        font-size: 18px;
        color: #999;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }


  .main-resource {
    display: flex;
    flex:1;

    .sidebar {
      width: 354px;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .top {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        .title {
          line-height: 32px;
          font-weight: bold;
        }
        .buts {
          gap:10px;
          display: flex;
          justify-content: space-between;
        }
      }

      .filter-group {
        margin-bottom: 20px;
        padding-left: 20px;

        .filter-title {
          font-size: 14px;
          font-weight: 400;
          margin-bottom: 20px;
        }
      }
    }

    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 20px;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .result-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        .res-text{
          margin-left: 15px;
          font-size: 14px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 0px;
          color: rgba(102, 102, 102, 1);
        }
      }

      .no-data {
        flex: 1;
        text-align: center;
        display: flex; 
        align-items: center; 
        justify-content: center;

        img {
          width: 200px;
        }

        .text {
          color: #999999;
        }
      }

	  .show-data{
		height: 100%;
	  }
    }
  }

  .main-paper {
      flex: 1;
      display: flex;

      .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 20px;
        background-color: #ffffff;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .result-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 20px;
          .res-text{
            font-size: 14px;
            font-weight: 400;
            letter-spacing: 0px;
            line-height: 0px;
            color: rgba(102, 102, 102, 1);
          }
        }

        .no-data {
          flex: 1;
          text-align: center;
          display: flex; 
          align-items: center; 
          justify-content: center;

          img {
            width: 200px;
          }

          .text {
            color: #999999;
          }
        }

		.show-data{
			height: 100%;
	  	}
      }
  }
}

.table {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

.table-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  overflow: auto;
}

.table-item {
  display: flex;
  flex-direction: row;
  gap: 15px;
}

.table-column {
  display: flex;
  flex-direction: column;
}

.icon {
  background-size: cover;
  width: 25px;
  height: 30px;
  cursor: pointer;
}

.resource-text {
	font-size: 16px;
	font-weight: 400;
	letter-spacing: 0px;
	line-height: 30px;
	cursor: pointer;

	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.paper-icon{
  background-size: cover;
  width: 60px;
  height: 60px;
  cursor: pointer;
}

.paper-title{
	font-size: 14px;
	font-weight: 400;
	letter-spacing: 0px;
	line-height: 20px;
	color: rgba(0, 0, 0, 1);
	margin-bottom: 5px;
	cursor: pointer;

	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.paper-text{
  	font-size: 12px;
	font-weight: 400;
	letter-spacing: 0px;
	line-height: 16.46px;
	color: rgba(102, 102, 102, 1);
	text-align: left;
	cursor: pointer;

	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;

}


.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}
:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}

</style>

