<template>
  <div class="course-class-page">
    <!-- 返回按钮 -->
    <back :url="'/course'" />
    <!-- 标题 -->
    <div class="title">
      {{ courseClassStore.title }}-班级管理
    </div>
    <!-- 班级管理 -->
    <ClassManager />
  </div>
</template>

<script lang="ts" setup>
//引入组件
import ClassManager from '@/components/courseClass/classManager.vue'
import back from '@/components/ui/back.vue'
//引入pinia
import { useCourseClassStore } from '@/stores/courseClass'
//获取课程id
const courseClassStore = useCourseClassStore()

</script>

<style scoped>
.course-class-page {
  background-image: url('@/assets/image/bg1.png');
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 100vh;
  padding: 0 40px 40px 40px;
  overflow: auto;
  min-width: 900px;
  width: 100%;

  .title {
      font: 20px bold;
      font-weight: 700;
      line-height: 24px;
      margin: 20px 0;
  }
}
</style>
