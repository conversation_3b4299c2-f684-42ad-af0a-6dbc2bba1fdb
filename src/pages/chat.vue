<script setup lang="ts">
  import { ChatProvider } from '@wicii/chat-primitive'
  import { useChat } from '@ai-sdk/vue'

  const helpers = useChat({
    api: '/chat-api/chat',
  })
</script>

<template>
  <ChatProvider :chat-context="helpers">
    <div class="flex h-screen flex-col bg-sky-100">
      <ChatMessageThread class="-mb-2 grow pb-4" />
      <ChatContainer class="pb-8">
        <ChatComposer />
      </ChatContainer>
    </div>
  </ChatProvider>
</template>
