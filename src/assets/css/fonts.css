@layer base {
  @font-face {
    font-family: 'alimamashuheiti';
    src: url('@/assets/font/阿里妈妈数黑体.ttf');
  }

  @font-face {
    font-family: 'alibaba<PERSON>huiti';
    src: url('@/assets/font/AlibabaPuHuiTi-3-45-Light.woff2') format('woff2');
    font-weight: 300;
  }

  @font-face {
    font-family: 'alibabapuhuiti';
    src: url('@/assets/font/AlibabaPuHuiTi-3-55-Regular.woff2') format('woff2');
    font-weight: 400;
  }

  @font-face {
    font-family: 'alibabapuhuiti';
    src: url('@/assets/font/AlibabaPuHuiTi-3-65-Medium.woff2') format('woff2');
    font-weight: 500;
  }

  @font-face {
    font-family: 'alibabapuhuiti';
    src: url('@/assets/font/AlibabaPuHuiTi-3-75-SemiBold.woff2') format('woff2');
    font-weight: 600;
  }

  @font-face {
    font-family: 'aliba<PERSON><PERSON>huiti';
    src: url('@/assets/font/AlibabaPuHuiTi-3-85-Bold.woff2') format('woff2');
    font-weight: 700;
  }

  @font-face {
    font-family: 'alibabapuhuiti';
    src: url('@/assets/font/AlibabaPuHuiTi-3-95-ExtraBold.woff2') format('woff2');
    font-weight: 800;
  }

  @font-face {
    font-family: 'alibabapuhuiti';
    src: url('@/assets/font/AlibabaPuHuiTi-3-105-Heavy.woff2') format('woff2');
    font-weight: 900;
  }

  @font-face {
    font-family: 'alibabapuhuiti';
    src: url('@/assets/font/AlibabaPuHuiTi-3-115-Black.woff2') format('woff2');
    font-weight: 1000;
  }
}
