.hide-scrollbar {
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
  background: transparent;
}

/* 整个滚动条 */
.scroll-container {
  width: 6px;
  height: 85px;
  overflow-y: auto;
}

/* 滚动条轨道 */
.scroll-container::-webkit-scrollbar {
  width: 8px;
}

.scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1; 
  border-radius: 4px;
}

/* 滚动条滑块 */
.scroll-container::-webkit-scrollbar-thumb {
  
  background-color: #C7DDFC;
  border-radius: 136px;
}

/* 滚动条滑块悬停状态 */
.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #555; 
}