import http from "@/services/http";

const AUTH_BASE_URL = "/api/v1/auth";

const AuthAPI = {
    login(data: LoginFormData) {
        return http<any, LoginResult>({
            url: `${AUTH_BASE_URL}/login`,
            method: "post",
            data: data,
            headers: {
                "Content-Type": "multipart/form-data"
            },
        })
    }
}

export default AuthAPI;

/** 登录表单数据 */
export interface LoginFormData {
    /** 用户名 */
    username: string;
    /** 密码 */
    password: string;
}
/** 登录响应 */
export interface LoginResult {
    /** 访问令牌 */
    accessToken: string;
    /** 刷新令牌 */
    refreshToken: string;
    /** 令牌类型 */
    tokenType: string;
    /** 过期时间(秒) */
    expiresIn: number;
  }