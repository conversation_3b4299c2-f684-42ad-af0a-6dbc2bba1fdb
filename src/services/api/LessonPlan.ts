import http from "@/services/http";

//获取教案列表
// api/v1/teachplan/?page=1&page_size=10&is_deleted=false
export const teachPlanList = (params: any) => {
    return http({
        url: `api/v1/teachplan/`,
        method: "get",
        params: params
    })
}

//搜索教案列表
// api/v1/teachplan/search_teachplan/?title=计算机
export const search_teachplan = (params: any) => {
    return http({
        url: `api/v1/teachplan/search_teachplan/`,
        method: "get",
        params: params
    })
}

//软删除教案
// api/v1/teachplan/soft_delete/
export const teachPlan_softDelete = (params: any) => {
    return http({
        url: `api/v1/teachplan/soft_delete/`,
        method: "post",
        data: params
    })
}

//硬删除教案
// api/v1/teachplan/hard_delete/
export const teachPlan_hardDelete = (params: any) => {
    return http({
        url: `api/v1/teachplan/hard_delete/`,
        method: "post",
        data: params
    })
}

//上传教案
// api/v1/teachplan/upload_file/
export const teachPlanUpload = (params: any) => {
    return http({
        url: `api/v1/teachplan/upload_file/`,
        headers: {
            "Content-Type": "multipart/form-data",
        },
        method: "post",
        data: params
    })
}

//生成教案(流式)
// api/v1/teachplan/lessons/
export const getTeachPlan = (teachPlanParams: any) =>{
    return fetch(`/api/api/v1/teachplan/lessons/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(teachPlanParams),
    })
}

//保存教案
// api/v1/teachplan/save_teachplan/
export const saveTeachPlan = (params:any) => {
    return http({
        url: 'api/v1/teachplan/save_teachplan/',
        method: "post",
        data: params
    })
}

//修改教案
// api/v1/teachplan/6/
export const editTeachPlan = (id:any, params:any) => {
    return http({
        url: `api/v1/teachplan/${id}/`,
        method: "put",
        data: params
    })
}


//获取教案模板列表
// api/v1/teachplanTemplate/?page=1&page_size=10&is_deleted=false
export const teachPlanTempList = (params: any) => { 
    return http({
        url: `api/v1/teachplanTemplate/`,
        method: "get",
        params: params
    })
}

//搜索教案模板
// api/v1/teachplanTemplate/search_teachplan/?title=人工
export const search_temp = (params: any) => {
    return http({
        url: `api/v1/teachplanTemplate/search_teachplan/`,
        method: "get",
        params: params
    })
}

//软删除教案模板
// api/v1/teachplanTemplate/soft_delete/
export const teachPlanTemp_softDelete = (params: any) => {
    return http({
        url: `api/v1/teachplanTemplate/soft_delete/`,
        method: "post",
        data: params
    })
}

//硬删除教案模板
// api/v1/teachplanTemplate/hard-delete/
export const teachPlanTemp_hardDelete = (params: any) => {
    return http({
        url: `api/v1/teachplanTemplate/hard-delete/`,
        method: "post",
        data: params
    })
}

//上传教案模板
// api/v1/teachplanTemplate/upload_file/
export const teachPlanTempUpload = (params: any) => {
    return http({
        url: `api/v1/teachplanTemplate/upload_file/`,
        headers: {
            "Content-Type": "multipart/form-data",
        },
        method: "post",
        data: params
    })
}

//生成教案模板(流式)
export const getTeachPlanTemp = (params:any) =>{
    return fetch(`/api/api/v1/teachplanTemplate/get_teachplan_template/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        theme:params.theme,
        document:params.document
      }),
    })
}

//保存教案模板
// api/v1/teachplanTemplate/save_teachplan_template/
export const saveTeachPlanTemp = (params:any) => {
    return http({
        url: 'api/v1/teachplanTemplate/save_teachplan_template/',
        method: "post",
        data: params
    })
}

//修改教案模板
// api/v1/teachplanTemplate/3/
export const editTeachPlanTemp = (id:any, params:any) => {
    return http({
        url: `api/v1/teachplanTemplate/${id}/`,
        method: "put",
        data: params
    })
}

//ai对话（流式）
export const aiConversation = (theme: string) =>{
    return fetch(`/api/api/v1/teachplan/conversation/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        "user_input" : theme
      }),
    })
}

