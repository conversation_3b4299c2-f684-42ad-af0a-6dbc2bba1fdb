import http from "@/services/http";

//获取图谱列表
export const graphList = (params:any) => {
    return http({
        url: 'api/v1/knowledge/list_graphs/',
        method: "get",
        params:params
    })
}

//创建知识图谱
export const createGraph = (params: any) => {
    return http({
        url: 'api/v1/knowledge/create_graph/',
        // 全局请求头为application/json; 上传文件需手动覆盖请求头类型为multipart/form-data
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        method: "post",
        data: params
    })
}

//新增节点
export const addGraphNode = (params: any) => {
    return http({
        url: 'api/v1/knowledge/add_node/',
        method: "post",
        data: params
    })
}

//删除节点
export const deleteGraphNode = (params: any) => {
    return http({
        url: 'api/v1/knowledge/delete_node/',
        method: "post",
        data: params
    })
}

//修改节点
export const updateGraphNode = (params: any) => {
    return http({
        url: 'api/v1/knowledge/update_node/',
        method: "put",
        data: params
    })
}

//查询节点
export const searchGraphNode = (params: any) => {
    return http({
        url: 'api/v1/knowledge/get_node_by_id/',
        method: "post",
        data: params
    })
}

//新增连线
export const addGraphEdge = (params: any) => {
    return http({
        url: 'api/v1/knowledge/add_edge/',
        method: "post",
        data: params
    })
}

//删除连线
export const deleteGraphEdge = (params: any) => {
    return http({
        url: 'api/v1/knowledge/delete_edge/',
        method: "post",
        data: params
    })
}

//修改连线
export const updateGraphEdge = (params: any) => {
    return http({
        // url: 'api/v1/knowledge/update_edge/',
        // 只改label
        url:'api/v1/knowledge/update_label/',
        method: "put",
        data: params
    })
}

//查询连线
export const searchGraphEdge = (params: any) => {
    return http({
        url: 'api/v1/knowledge/get_edge_by_id/',
        method: "post",
        data: params
    })
}

//知识图谱提取
// export const createGraph = (params: any) => {
//     return http({
//         url: 'api/v1/knowledge/get_knowledge_graph/',
//         // 全局请求头为application/json; 上传文件需手动覆盖请求头类型为multipart/form-data
//         headers: {
//             'Content-Type': 'multipart/form-data'
//         },
//         method: "post",
//         data: params
//     })
// }


