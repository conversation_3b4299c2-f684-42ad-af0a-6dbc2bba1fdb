import http from "@/services/http";
//获取考试/作业列表
export const getExamList = (params: any) => {
    return http({
        url: '/api/v1/exam/',
        method: "get",
        params: params
    })
}
//新增考试/作业
export const creatExam = (params: any) => {
    return http({
        url: '/api/v1/exam/',
        method: "post",
        data: params
    })
}
//编辑考试/作业
export const editExam = (params: any) => {
    return http({
        url: `/api/v1/exam/${params.id}/`,
        method: "put",
        data: params
    })
}

//加入考试/作业
export const joinExam = (params: any) => {
    return http({
        url: '/api/v1/paper/',
        method: "post",
        data: params
    })
}

//删除考试
export const deleteExam = (params: any) => {
    return http({
        url: '/api/v1/exam/delete/',
        method: "post",
        data: params
    }) 
}
