import http from "@/services/http";
import axios from "axios";

//解析文件
export const fileAnalysis = (params: any) => {
    return http({
        url: 'api/v1/analysis/',
        // 全局请求头为application/json; 上传文件需手动覆盖请求头类型为multipart/form-data
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        method: "post",
        data: params
    })
}

//摘要提取
// export const createAbstract = (params: any) => {
//     return http({
//         url: 'api/v1/analysis/ai/',
//         // 全局请求头为application/json; 上传文件需手动覆盖请求头类型为multipart/form-data
//         headers: {
//             'Content-Type': 'multipart/form-data'
//         },
//         method: "post",
//         data: params
//     })
// }

//摘要提取(测试)
export const createAbstract = (params: any) => {
    return axios.post('http://**************:18000/api/v1/analysis/ai/', {
        headers: {
            'Content-Type': 'application/json'
        },
        data: params
    })
}
