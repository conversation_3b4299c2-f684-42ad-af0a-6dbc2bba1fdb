// api/v1/ppt/?page=1&page_size=10&is_deleted=true
import http from "@/services/http";

//题目列表
export const pptList = (params: any) => {
    return http({
        url: `api/v1/ppt/`,
        method: "get",
        params: params
    })
}
//ppt软删除
// api/v1/teachplan/soft_delete/
export const ppt_softDelete = (params: any) => {
    return http({
        url: `api/v1/ppt/soft_delete/`,
        method: "post",
        data: params
    })
}

//ppt硬删除
// api/v1/teachplan/hard_delete/
export const ppt_hardDelete = (params: any) => {
    return http({
        url: `api/v1/ppt/hard_delete/`,
        method: "post",
        data: params
    })
}

//上传PPT
// api/v1/ppt/
export const pptUpload = (params: any) => {
    return http({
        url: `api/v1/ppt/`,
        headers: {
            "Content-Type": "multipart/form-data",
        },
        method: "post",
        data: params
    })
}