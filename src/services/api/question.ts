import http from "@/services/http";

//题目列表
// api/v1/question/?page_size=20&page=1
export const questionList = (params:any) => {
    return http({
        url: `api/v1/question/`,
        method: "get",
        params: params
    })
}

//题目详情
export const selectQuestion = (qid: any) => {
    return http({
        url: `api/v1/question/${qid}/`,
        method: "get"
    })
}

//删除题目
export const deleteQuestions = (params: any) => {
    return http({
        url: `api/v1/question/${params.id}/`,
        method: "delete",
    })
}

//批量删除
export const deleteQuestionsBatch = (params: any) => {
    return http({
        url: `api/v1/question/soft_delete/`,
        method: "post",
        data: params
    })
}


//新增单选题目
export const addSingleQuestions = (params: any) => {
    return http({
        url: '/api/v1/single_question/',
        method: "post",
        data: params
    })
}

//修改单选题目
export const editSingleQuestions = (params: any) => {
    return http({
        url: `/api/v1/single_question/${params.id}/`,
        method: "put",
        data: params
    })
}


//新增多选题目
export const addMultipleQuestions = (params: any) => {
    return http({
        url: '/api/v1/multi_question/',
        method: "post",
        data: params
    })
}
//修改多选题目
export const editMultipleQuestions = (params: any) => {
    return http({
        url: `/api/v1/multi_question/${params.id}/`,
        method: "put",
        data: params
    })
}


//新增判断题目
export const addJudgeQuestions = (params: any) => {
    return http({
        url: '/api/v1/tf_question/',
        method: "post",
        data: params
    })
}

//修改判断题目
export const editJudgeQuestions = (params: any) => {
    return http({
        url: `/api/v1/tf_question/${params.id}/`,
        method: "put",
        data: params
    })
}

//新增填空题目
export const addFillQuestions = (params: any) => {
    return http({
        url: '/api/v1/fill_question/',
        method: "post",
        data: params
    })
}

//修改填空题目
export const editFillQuestions = (params: any) => {
    return http({
        url: `/api/v1/fill_question/${params.id}/`,
        method: "put",
        data: params
    })
}

//新增问答题
export const addAnswerQuestions = (params: any) => {
    return http({
        url: '/api/v1/qa_question/',
        method: "post",
        data: params
    })
}
//修改问答题
export const editAnswerQuestions = (params: any) => {
    return http({
        url: `/api/v1/qa_question/${params.id}/`,
        method: "put",
        data: params
    })
}

//ai生成题目
export const aiQuestions = (params: any) => {
    return http({
        url: '/api/v1/ai_question/generate/',
        method: "post",
        headers: {
            "Content-Type": "multipart/form-data",
        },
        data: params
    })
}

//批量导入试题
export const batchCreate = (params: any) => {
    return http({
        url: '/api/v1/question/batch_create/',
        method: "post",
        data: params
    })
}


//上传文件批量导入试题
export const excelAddQue = (params: any) => {
    return http({
        url: '/api/v1/question/excel_upload/',
        method: "post",
        data: params
    })
}


//题目归属人api/v1/question/user_list/28/

export const questionUserList = (params?: any) => {
    return http({
        url: `/api/v1/question/user_list/${params}/`,
        method: "get",
    })
}