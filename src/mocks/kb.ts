import { http, HttpResponse } from 'msw'
import { faker } from '@faker-js/faker'

const API_BASE = '/kb-api/api/v1'

// 生成100条模拟数据
const generateMockKbData = (): API.Kb.Kb[] => {
  return Array.from({ length: 100 }, () => {
    const id = faker.string.uuid()
    const createTime = faker.date.past({ years: 2 }).getTime()
    const updateTime = faker.date
      .between({
        from: new Date(createTime),
        to: new Date(),
      })
      .getTime()

    return {
      id,
      name: faker.company.name() + '知识库',
      description: faker.lorem.paragraphs(),
      chunk_count: faker.number.int({ min: 10, max: 1000 }),
      document_count: faker.number.int({ min: 1, max: 50 }),
      token_num: faker.number.int({ min: 1000, max: 100000 }),
      create_date: new Date(createTime).toISOString().split('T')[0],
      create_time: createTime,
      update_date: new Date(updateTime).toISOString().split('T')[0],
      update_time: updateTime,
      created_by: faker.person.fullName(),
      language: faker.helpers.arrayElement(['zh', 'en', 'zh-CN']),
      pagerank: faker.number.float({ min: 0, max: 1, fractionDigits: 3 }),
      chunk_method: faker.helpers.arrayElement(['text-splitter', 'naive', 'manual']),
      permission: faker.helpers.arrayElement(['me', 'team']),
      similarity_threshold: faker.number.float({ min: 0.1, max: 0.9, fractionDigits: 2 }),
      status: faker.helpers.arrayElement(['active', 'inactive', 'processing']),
      tenant_id: faker.string.uuid(),
      vector_similarity_weight: faker.number.float({ min: 0, max: 1, fractionDigits: 2 }),
      embedding_model: faker.lorem.slug(),
      nickname: faker.person.firstName(),
      parser_config: {
        chunk_token_num: faker.number.int({ min: 100, max: 1000 }),
        delimiter: faker.helpers.arrayElement([';', ',', '.', '!', '?']),
        html4excel: faker.datatype.boolean(),
        layout_recognize: faker.datatype.boolean(),
        auto_keywords: faker.number.int({ min: 1, max: 10 }),
        auto_questions: faker.number.int({ min: 1, max: 5 }),
        pages: Array.from(
          { length: faker.number.int({ min: 1, max: 3 }) },
          () =>
            [faker.number.int({ min: 1, max: 50 }), faker.number.int({ min: 51, max: 100 })] as [
              number,
              number,
            ],
        ),
        raptor: {
          use_raptor: faker.datatype.boolean(),
        },
      },
    }
  })
}

// 生成100条模拟文档数据
const generateMockFileData = (): API.Kb.File[] => {
  return Array.from({ length: 100 }, () => {
    const id = faker.string.uuid()
    const createTime = faker.date.past({ years: 2 }).getTime()
    const updateTime = faker.date
      .between({
        from: new Date(createTime),
        to: new Date(),
      })
      .getTime()

    return {
      id,
      kb_id: faker.string.uuid(),
      name: faker.system.fileName(),
      location: faker.system.directoryPath() + '/' + faker.system.fileName(),
      chunk_count: faker.number.int({ min: 5, max: 500 }),
      create_date: new Date(createTime).toISOString().split('T')[0],
      create_time: createTime,
      created_by: faker.person.fullName(),
      parser_config: {
        chunk_token_num: faker.number.int({ min: 100, max: 1000 }),
        delimiter: faker.helpers.arrayElement([';', ',', '.', '!', '?']),
        html4excel: faker.datatype.boolean(),
        layout_recognize: faker.helpers.arrayElement(['DeepDoc', 'Plain Text']),
        pages: Array.from(
          { length: faker.number.int({ min: 1, max: 3 }) },
          () =>
            [faker.number.int({ min: 1, max: 50 }), faker.number.int({ min: 51, max: 100 })] as [
              number,
              number,
            ],
        ),
        auto_keywords: faker.number.int({ min: 1, max: 10 }),
        auto_questions: faker.number.int({ min: 1, max: 5 }),
        raptor: {
          use_raptor: faker.datatype.boolean(),
        },
        graphrag: {
          community: faker.datatype.boolean(),
          entity_types: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () =>
            faker.helpers.arrayElement(['Person', 'Organization', 'Location', 'Event', 'Product']),
          ),
          method: faker.helpers.arrayElement(['global', 'local']),
          resolution: faker.datatype.boolean(),
          use_graphrag: faker.datatype.boolean(),
        },
      },
      chunk_method: faker.helpers.arrayElement(['text-splitter', 'naive', 'manual']),
      process_begin_at: faker.date.recent().toISOString(),
      process_duation: faker.number.int({ min: 10, max: 3600 }), // 秒数
      progress: faker.number.int({ min: 0, max: 100 }),
      progress_msg: faker.helpers.arrayElement([
        '正在解析文档...',
        '正在分割文本...',
        '正在生成向量...',
        '解析完成',
        '解析失败',
        '等待处理',
      ]),
      run: faker.helpers.arrayElement(['UNSTART', 'RUNNING', 'CANCEL', 'DONE', 'FAIL'] as const),
      size: faker.number.int({ min: 1024, max: 10485760 }), // 文件大小，字节
      source_type: faker.helpers.arrayElement(['upload', 'url', 'api']),
      status: faker.helpers.arrayElement(['active', 'inactive', 'processing', 'failed']),
      thumbnail: faker.image.url({ width: 200, height: 200 }),
      token_num: faker.number.int({ min: 100, max: 50000 }),
      type: faker.helpers.arrayElement(['pdf', 'doc', 'txt', 'md', 'docx']),
      update_date: new Date(updateTime).toISOString().split('T')[0],
      update_time: updateTime,
      meta_fields: {
        author: faker.person.fullName(),
        subject: faker.lorem.sentence(),
        keywords: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () =>
          faker.lorem.word(),
        ).join(','),
        created_at: faker.date.past().toISOString(),
        modified_at: faker.date.recent().toISOString(),
      },
    }
  })
}

// 缓存生成的数据，避免每次请求都重新生成
const mockData = generateMockKbData()
const mockFileData = generateMockFileData()

export const kbHandlers = [
  http.get(`${API_BASE}/datasets/`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('page_size') || '10')
    const idParam = url.searchParams.get('id')
    const source = url.searchParams.get('source')

    // 参数验证
    if (page < 1) {
      return HttpResponse.json({ error: 'Page must be greater than 0' }, { status: 400 })
    }

    let filteredData = mockData

    // 如果提供了 id 参数，则随机返回一条数据
    if (idParam) {
      const randomIndex = faker.number.int({ min: 0, max: mockData.length - 1 })
      filteredData = [mockData[randomIndex]]
    }

    // 计算分页
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize

    // 处理超出范围的情况
    if (startIndex >= filteredData.length) {
      // 根据source参数返回不同格式
      if (source === 'external') {
        return HttpResponse.json({
          data: {
            data: [],
          },
        })
      } else {
        return HttpResponse.json({
          data: {
            datasets: [],
          },
        })
      }
    }

    const paginatedData = filteredData.slice(startIndex, endIndex)

    // 根据source参数返回不同格式
    if (source === 'external') {
      // [id].vue 页面期望的格式
      return HttpResponse.json({
        data: {
          data: paginatedData,
        },
      })
    } else {
      // index.vue 页面期望的格式 (source === 'local')
      return HttpResponse.json({
        data: {
          datasets: paginatedData,
        },
      })
    }
  }),

  // 获取知识库文档列表
  http.get(`${API_BASE}/datasets/:id/documents/`, ({ request, params }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('page_size') || '10')
    const kbId = params.id as string

    // 参数验证
    if (page < 1) {
      return HttpResponse.json({ error: 'Page must be greater than 0' }, { status: 400 })
    }

    if (!kbId) {
      return HttpResponse.json({ error: 'Dataset ID is required' }, { status: 400 })
    }

    // 为指定知识库生成文档数据（模拟关联）
    const kbFileData = mockFileData.map((file) => ({
      ...file,
      kb_id: kbId, // 将文档关联到指定的知识库ID
    }))

    // 计算分页
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize

    // 处理超出范围的情况
    if (startIndex >= kbFileData.length) {
      return HttpResponse.json({
        data: {
          data: {
            docs: [],
          },
        },
      })
    }

    const paginatedData = kbFileData.slice(startIndex, endIndex)

    // Return format expected by KbFilesPage.vue: { data: { data: { docs: API.Kb.File[] } } }
    return HttpResponse.json({
      data: {
        data: {
          docs: paginatedData,
        },
      },
    })
  }),
]
