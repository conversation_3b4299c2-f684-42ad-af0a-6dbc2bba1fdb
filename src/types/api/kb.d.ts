declare namespace API {
  declare namespace Kb {
    export interface Kb {
      avatar?: string
      chunk_count: number
      create_date?: string
      create_time?: number
      created_by?: string
      description?: string
      document_count: number
      id: string
      language?: string
      name: string
      chunk_method: string
      pagerank: number
      parser_config: KbParserConfig
      permission: string
      similarity_threshold?: number
      status?: string
      tenant_id?: string
      token_num: number
      update_date?: string
      update_time?: number
      vector_similarity_weight?: number
      embedding_model: string
      nickname?: string
    }

    export interface KbParserConfig {
      auto_keywords?: number
      auto_questions?: number
      chunk_token_num?: number
      delimiter?: string
      html4excel?: boolean
      layout_recognize?: boolean
      pages?: [number, number][]
      raptor?: Raptor
    }

    export interface Raptor {
      use_raptor: boolean
    }

    interface File {
      chunk_count: number
      create_date: string
      create_time: number
      created_by: string
      id: string
      kb_id: string
      location: string
      name: string
      parser_config: ParserConfig
      chunk_method: string
      process_begin_at?: string
      process_duation: number
      progress: number
      progress_msg: string
      run: DocumentRunningStatus
      size: number
      source_type: string
      status: string
      thumbnail: string
      token_num: number
      type: string
      update_date: string
      update_time: number
      meta_fields?: Record<string, unknown>
    }

    interface ParserConfig {
      chunk_token_num?: number
      delimiter?: string
      html4excel?: boolean
      layout_recognize?: 'DeepDoc' | 'Plain Text'
      pages?: [number, number][]
      auto_keywords?: number
      auto_questions?: number
      raptor?: {
        use_raptor: boolean
      }
      graphrag?: GraphRag
    }

    interface GraphRag {
      community?: boolean
      entity_types?: string[]
      method?: string
      resolution?: boolean
      use_graphrag?: boolean
    }

    interface Chunk {
      available_int: number // Whether to enable, 0: not enabled, 1: enabled
      chunk_id: string
      content_with_weight: string
      doc_id: string
      doc_name: string
      img_id: string
      important_kwd?: string[]
      question_kwd?: string[] // keywords
      tag_kwd?: string[]
      positions: number[][]
      tag_feas?: Record<string, number>
    }

    type DocumentRunningStatus =
      | 'UNSTART' // need to run
      | 'RUNNING' // need to cancel
      | 'CANCEL' // need to refresh
      | 'DONE' // need to refresh
      | 'FAIL' // need to refresh
  }
}
