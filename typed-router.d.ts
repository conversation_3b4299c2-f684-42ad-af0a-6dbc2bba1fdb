/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/(app)': RouteRecordInfo<'/(app)', '/', Record<never, never>, Record<never, never>>,
    '/(app)/': RouteRecordInfo<'/(app)/', '/', Record<never, never>, Record<never, never>>,
    '/(app)/abstract/': RouteRecordInfo<'/(app)/abstract/', '/abstract', Record<never, never>, Record<never, never>>,
    '/(app)/ai': RouteRecordInfo<'/(app)/ai', '/ai', Record<never, never>, Record<never, never>>,
    '/(app)/aiChat/': RouteRecordInfo<'/(app)/aiChat/', '/aiChat', Record<never, never>, Record<never, never>>,
    '/(app)/aiSpace/': RouteRecordInfo<'/(app)/aiSpace/', '/aiSpace', Record<never, never>, Record<never, never>>,
    '/(app)/course/': RouteRecordInfo<'/(app)/course/', '/course', Record<never, never>, Record<never, never>>,
    '/(app)/course/[id]': RouteRecordInfo<'/(app)/course/[id]', '/course/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/(app)/course/chapter//[id]': RouteRecordInfo<'/(app)/course/chapter//[id]', '/course/chapter/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/(app)/course/courseAnalysis': RouteRecordInfo<'/(app)/course/courseAnalysis', '/course/courseAnalysis', Record<never, never>, Record<never, never>>,
    '/(app)/course/courseAnalysis_[id]': RouteRecordInfo<'/(app)/course/courseAnalysis_[id]', '/course/courseAnalysis_:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/(app)/courseClass/': RouteRecordInfo<'/(app)/courseClass/', '/courseClass', Record<never, never>, Record<never, never>>,
    '/(app)/graph/': RouteRecordInfo<'/(app)/graph/', '/graph', Record<never, never>, Record<never, never>>,
    '/(app)/graph/[id]': RouteRecordInfo<'/(app)/graph/[id]', '/graph/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/(app)/graph/index_old': RouteRecordInfo<'/(app)/graph/index_old', '/graph/index_old', Record<never, never>, Record<never, never>>,
    '/(app)/graph/index_v0': RouteRecordInfo<'/(app)/graph/index_v0', '/graph/index_v0', Record<never, never>, Record<never, never>>,
    '/(app)/homework/': RouteRecordInfo<'/(app)/homework/', '/homework', Record<never, never>, Record<never, never>>,
    '/(app)/homework/create': RouteRecordInfo<'/(app)/homework/create', '/homework/create', Record<never, never>, Record<never, never>>,
    '/(app)/homework/grade': RouteRecordInfo<'/(app)/homework/grade', '/homework/grade', Record<never, never>, Record<never, never>>,
    '/(app)/homework/handquetion': RouteRecordInfo<'/(app)/homework/handquetion', '/homework/handquetion', Record<never, never>, Record<never, never>>,
    '/(app)/homework/homeWorkAnalysis': RouteRecordInfo<'/(app)/homework/homeWorkAnalysis', '/homework/homeWorkAnalysis', Record<never, never>, Record<never, never>>,
    '/(app)/homework/list': RouteRecordInfo<'/(app)/homework/list', '/homework/list', Record<never, never>, Record<never, never>>,
    '/(app)/kb': RouteRecordInfo<'/(app)/kb', '/kb', Record<never, never>, Record<never, never>>,
    '/(app)/kb/': RouteRecordInfo<'/(app)/kb/', '/kb', Record<never, never>, Record<never, never>>,
    '/(app)/kb/[id]': RouteRecordInfo<'/(app)/kb/[id]', '/kb/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/(app)/LessonPlan/': RouteRecordInfo<'/(app)/LessonPlan/', '/LessonPlan', Record<never, never>, Record<never, never>>,
    '/(app)/LessonPlan/afterSelectTemp': RouteRecordInfo<'/(app)/LessonPlan/afterSelectTemp', '/LessonPlan/afterSelectTemp', Record<never, never>, Record<never, never>>,
    '/(app)/LessonPlan/createTeachPlanTemp': RouteRecordInfo<'/(app)/LessonPlan/createTeachPlanTemp', '/LessonPlan/createTeachPlanTemp', Record<never, never>, Record<never, never>>,
    '/(app)/LessonPlan/teachPlanEditor': RouteRecordInfo<'/(app)/LessonPlan/teachPlanEditor', '/LessonPlan/teachPlanEditor', Record<never, never>, Record<never, never>>,
    '/(app)/LessonPlan/teachPlanManage': RouteRecordInfo<'/(app)/LessonPlan/teachPlanManage', '/LessonPlan/teachPlanManage', Record<never, never>, Record<never, never>>,
    '/(app)/LessonPlan/teachPlanRecycle': RouteRecordInfo<'/(app)/LessonPlan/teachPlanRecycle', '/LessonPlan/teachPlanRecycle', Record<never, never>, Record<never, never>>,
    '/(app)/LessonPlan/teachPlanTempEditor': RouteRecordInfo<'/(app)/LessonPlan/teachPlanTempEditor', '/LessonPlan/teachPlanTempEditor', Record<never, never>, Record<never, never>>,
    '/(app)/LessonPlan/teachPlanTempManage': RouteRecordInfo<'/(app)/LessonPlan/teachPlanTempManage', '/LessonPlan/teachPlanTempManage', Record<never, never>, Record<never, never>>,
    '/(app)/LessonPlan/teachPlanTempRecycle': RouteRecordInfo<'/(app)/LessonPlan/teachPlanTempRecycle', '/LessonPlan/teachPlanTempRecycle', Record<never, never>, Record<never, never>>,
    '/(app)/models': RouteRecordInfo<'/(app)/models', '/models', Record<never, never>, Record<never, never>>,
    '/(app)/pptSquare/': RouteRecordInfo<'/(app)/pptSquare/', '/pptSquare', Record<never, never>, Record<never, never>>,
    '/(app)/pptSquare/pptManage': RouteRecordInfo<'/(app)/pptSquare/pptManage', '/pptSquare/pptManage', Record<never, never>, Record<never, never>>,
    '/(app)/pptSquare/pptRecycle': RouteRecordInfo<'/(app)/pptSquare/pptRecycle', '/pptSquare/pptRecycle', Record<never, never>, Record<never, never>>,
    '/(app)/quetionList/': RouteRecordInfo<'/(app)/quetionList/', '/quetionList', Record<never, never>, Record<never, never>>,
    '/(app)/resources': RouteRecordInfo<'/(app)/resources', '/resources', Record<never, never>, Record<never, never>>,
    '/auth/': RouteRecordInfo<'/auth/', '/auth', Record<never, never>, Record<never, never>>,
    '/chapter/edit': RouteRecordInfo<'/chapter/edit', '/chapter/edit', Record<never, never>, Record<never, never>>,
    '/chapter/view': RouteRecordInfo<'/chapter/view', '/chapter/view', Record<never, never>, Record<never, never>>,
    '/chat': RouteRecordInfo<'/chat', '/chat', Record<never, never>, Record<never, never>>,
    '/exam/': RouteRecordInfo<'/exam/', '/exam', Record<never, never>, Record<never, never>>,
    '/question/addQuestion': RouteRecordInfo<'/question/addQuestion', '/question/addQuestion', Record<never, never>, Record<never, never>>,
    '/question/aiAddQuestion': RouteRecordInfo<'/question/aiAddQuestion', '/question/aiAddQuestion', Record<never, never>, Record<never, never>>,
    '/question/ailist': RouteRecordInfo<'/question/ailist', '/question/ailist', Record<never, never>, Record<never, never>>,
    '/question/list': RouteRecordInfo<'/question/list', '/question/list', Record<never, never>, Record<never, never>>,
  }
}
