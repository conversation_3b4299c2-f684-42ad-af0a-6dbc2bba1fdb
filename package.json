{"name": "ai-education", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "dotenvx run -- vite", "build": "dotenvx run --ignore=MISSING_ENV_FILE -- vite build", "preview": "vite preview", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"3d-force-graph": "^1.77.0", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/vue": "^1.2.12", "@ant-design/icons-vue": "^7.0.1", "@crazydos/vue-markdown": "^1.1.3", "@element-plus/icons-vue": "^2.3.1", "@faker-js/faker": "^9.8.0", "@iconify-json/devicon": "^1.2.28", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify-json/vscode-icons": "^1.2.23", "@t3-oss/env-core": "^0.13.8", "@tanstack/vue-query": "^5.80.2", "@uppy/core": "^4.4.6", "@uppy/dashboard": "^4.3.4", "@uppy/drag-drop": "^4.1.3", "@uppy/file-input": "^4.1.3", "@uppy/locales": "^4.5.3", "@uppy/progress-bar": "^4.2.1", "@uppy/vue": "^2.2.0", "@uppy/xhr-upload": "^4.3.3", "@vue-office/docx": "^1.6.3", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "@vueuse/core": "^13.3.0", "@vueuse/integrations": "^13.3.0", "@wicii/chat-primitive": "^0.2.4", "@xstate/vue": "^4.0.4", "ai": "^4.3.16", "aieditor": "^1.3.9", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "docx": "^9.5.0", "element-plus": "^2.10.2", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "html2pdf.js": "^0.10.3", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "msw": "^2.10.2", "ofetch": "^1.4.1", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.3.0", "reka-ui": "^2.3.1", "remark-gfm": "^4.0.1", "sass": "^1.89.2", "shiki": "^3.7.0", "tailwind-merge": "^3.3.0", "three": "^0.169.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-demi": "0.14.6", "vue-router": "^4.5.0", "vue3-lottie": "^3.3.1", "vue3-video-play": "^1.3.2", "xml-js": "^1.6.11", "xstate": "^5.19.4", "zod": "^3.25.67"}, "devDependencies": {"@dotenvx/dotenvx": "^1.45.1", "@iconify-json/ant-design": "^1.2.5", "@iconify-json/arcticons": "^1.2.29", "@iconify-json/lucide": "^1.2.45", "@iconify-json/material-symbols": "^1.2.28", "@tailwindcss/vite": "^4.1.8", "@tsconfig/node22": "^22.0.1", "@types/d3": "^7.4.3", "@types/file-saver": "^2.0.7", "@types/markdown-it": "^14.1.2", "@types/node": "^22.14.0", "@types/three": "^0.177.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/compiler-sfc": "^3.5.17", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "less": "^4.3.0", "lightningcss": "^1.30.1", "mini-css-extract-plugin": "^2.9.2", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.7.0", "unplugin-vue-router": "^0.12.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}, "msw": {"workerDirectory": ["public"]}}