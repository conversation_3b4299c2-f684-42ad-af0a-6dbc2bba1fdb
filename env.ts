import { createEnv } from '@t3-oss/env-core'
import { z } from 'zod'

export const env = createEnv({
  client: {
    VITE_ENABLE_MOCKING: z
      .string()
      .refine((s) => s === 'true' || s === 'false')
      .transform((s) => s === 'true')
      .optional()
      .default('false'),
    VITE_PPT_FRONTEND_URL: z.string().url(),
    VITE_API_BASE_URL: z.string().optional().default('/api'),
  },
  clientPrefix: 'VITE_',

  // vite build 时用的环境变量，不会暴露给前端
  server: {
    BACKEND_URL: z.string().url().optional().default('https://ai-alpha.tianhe-tech.com/api'),
  },
  runtimeEnv: import.meta.env,
  emptyStringAsUndefined: true,
})
